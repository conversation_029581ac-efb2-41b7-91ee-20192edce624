# Booking Calendar Theme App Extension

This theme app extension provides a booking calendar interface for products on Shopify product pages. It allows customers to select dates and times for bookable products directly from the product page.

## Features

- **Interactive Calendar**: Visual calendar interface for date selection
- **Time Slot Selection**: Support for hourly and daily booking types
- **Real-time Availability**: Shows available and unavailable time slots
- **Responsive Design**: Works on desktop and mobile devices
- **Customizable Styling**: Configurable colors and appearance
- **Multi-language Support**: Translatable text strings
- **Cart Integration**: Seamlessly integrates with Shopify cart

## Installation

1. The extension is automatically included when you install the Booky app
2. Navigate to your theme editor in the Shopify admin
3. Go to a product page template
4. Add the "Booking Calendar" app block to your product page
5. Configure the settings as needed

## Configuration

### App Block Settings

- **Product**: Auto-fills with the current product (required)
- **Primary Color**: Main color for calendar elements (#007cba default)
- **Accent Color**: Color for selected dates and highlights (#28a745 default)
- **Show Unavailable Dates**: Whether to display unavailable dates (enabled by default)

### Product Setup

For the booking calendar to appear, products must have:

1. A booking configuration created through the Booky app admin interface
2. The booking configuration must be active
3. The product metafield `booky.booking_config` must be populated

## Usage

### For Merchants

1. **Create Booking Configuration**:
   - Go to the Booky app in your Shopify admin
   - Navigate to "Booking Management"
   - Create a new booking configuration for your product
   - Set the booking type (hourly/daily), duration, and availability

2. **Add to Theme**:
   - Open the theme editor
   - Navigate to a product page
   - Add the "Booking Calendar" app block
   - Position it where you want the calendar to appear
   - Save your changes

3. **Customize Appearance**:
   - Adjust the primary and accent colors to match your brand
   - Configure which dates to show/hide
   - Test the calendar functionality

### For Customers

1. **Select Date**: Click on an available date in the calendar
2. **Choose Time** (for hourly bookings): Select from available time slots
3. **Add to Cart**: The booking details are automatically added to the cart
4. **Checkout**: Complete the purchase with booking information included

## Technical Details

### Files Structure

```
booking-calendar-theme/
├── assets/
│   ├── booking-calendar.css    # Styling for the calendar
│   └── booking-calendar.js     # Calendar functionality
├── blocks/
│   └── booking_calendar.liquid # Main app block template
├── snippets/
│   └── booking-helpers.liquid  # Reusable Liquid helpers
├── locales/
│   └── en.default.json        # English translations
└── shopify.extension.toml     # Extension configuration
```

### API Integration

The calendar fetches data from:
- `/apps/booky/api/booking-slots` - Gets available slots and configuration
- Integrates with the main Booky app database

### Metafields Used

- `booky.booking_config` - Contains booking configuration data
- Product properties are added to cart for booking details

### Cart Integration

When a booking is selected, the following properties are added to the cart:

- `Booking Date` - Selected date
- `Booking Time` - Selected time (for hourly bookings)
- `Booking Duration` - Duration in minutes
- `_booking_enabled` - Internal flag
- `_booking_config_id` - Configuration reference
- `_booking_slot_type` - HOURLY or DAILY
- `_booking_price_adjustment` - Additional fees

## Customization

### Styling

The calendar can be customized by modifying `booking-calendar.css`:

- Colors: Update CSS custom properties
- Layout: Modify grid and flexbox properties
- Typography: Change font families and sizes
- Responsive: Adjust media queries

### Translations

Add translations in locale files:

```json
{
  "booking": {
    "calendar": {
      "title": "Select Booking Date",
      "loading": "Loading available dates...",
      "selected_date": "Selected Date:",
      "select_time": "Select Time:",
      "choose_time": "Choose a time...",
      "duration": "Duration:",
      "minutes": "minutes",
      "additional_cost": "Booking fee:"
    }
  }
}
```

### JavaScript Customization

The `BookingCalendar` class can be extended or modified:

- Add custom validation
- Integrate with third-party services
- Modify the calendar appearance
- Add additional features

## Troubleshooting

### Calendar Not Showing

1. Check if the product has a booking configuration
2. Verify the booking configuration is active
3. Ensure the app block is added to the product template
4. Check browser console for JavaScript errors

### Dates Not Loading

1. Verify API connectivity
2. Check booking configuration settings
3. Ensure advance booking days are set correctly
4. Check for server-side errors in app logs

### Styling Issues

1. Check CSS file is loading correctly
2. Verify theme compatibility
3. Check for CSS conflicts with theme styles
4. Test on different devices and browsers

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- Mobile browsers (iOS Safari, Chrome Mobile)

## Performance

- Calendar renders client-side for fast interaction
- API calls are cached for better performance
- Responsive images and optimized assets
- Minimal JavaScript footprint

## Security

- All API calls are authenticated
- Input validation on both client and server
- CSRF protection enabled
- Secure handling of customer data

## Support

For technical support or feature requests:
1. Check the Booky app documentation
2. Contact support through the Shopify Partner Dashboard
3. Review the app logs for error details
