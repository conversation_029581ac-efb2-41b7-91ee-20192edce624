{% comment %}
  Booking Calendar App Block
  This block displays a booking calendar for products that have booking configurations
{% endcomment %}

{% assign product = block.settings.product %}
{% assign booking_config = product.metafields.booky.booking_config.value %}

{% if booking_config %}
  <div class="booking-calendar-container" data-product-id="{{ product.id }}">
    <h3 class="booking-calendar-title">{{ 'booking.calendar.title' | t: default: 'Select Booking Date' }}</h3>

    <div class="booking-calendar-wrapper">
      <div id="booking-calendar-{{ product.id }}" class="booking-calendar">
        <div class="calendar-loading">
          {{ 'booking.calendar.loading' | t: default: 'Loading available dates...' }}
        </div>
      </div>

      <div class="booking-details" id="booking-details-{{ product.id }}" style="display: none;">
        <div class="selected-date">
          <strong>{{ 'booking.calendar.selected_date' | t: default: 'Selected Date:' }}</strong>
          <span id="selected-date-display-{{ product.id }}"></span>
        </div>

        {% if booking_config.slot_type == 'HOURLY' %}
          <div class="time-slots">
            <label for="time-slot-{{ product.id }}">{{ 'booking.calendar.select_time' | t: default: 'Select Time:' }}</label>
            <select id="time-slot-{{ product.id }}" class="time-slot-select">
              <option value="">{{ 'booking.calendar.choose_time' | t: default: 'Choose a time...' }}</option>
            </select>
          </div>
        {% endif %}

        <div class="booking-duration">
          <span>{{ 'booking.calendar.duration' | t: default: 'Duration:' }} {{ booking_config.slot_duration }} {{ 'booking.calendar.minutes' | t: default: 'minutes' }}</span>
        </div>

        {% if booking_config.price_adjustment > 0 %}
          <div class="booking-price">
            <span>{{ 'booking.calendar.additional_cost' | t: default: 'Booking fee:' }} {{ booking_config.price_adjustment | money }}</span>
          </div>
        {% endif %}
      </div>
    </div>

    <input type="hidden" id="booking-date-{{ product.id }}" name="properties[Booking Date]" value="">
    <input type="hidden" id="booking-time-{{ product.id }}" name="properties[Booking Time]" value="">
    <input type="hidden" id="booking-duration-{{ product.id }}" name="properties[Booking Duration]" value="{{ booking_config.slot_duration }}">
    <input type="hidden" id="booking-total-price-{{ product.id }}" name="properties[Booking Total Price]" value="">
    <input type="hidden" id="booking-price-adjustments-{{ product.id }}" name="properties[Booking Price Adjustments]" value="">
    <input type="hidden" id="booking-original-price-{{ product.id }}" name="properties[Booking Original Price]" value="">
  </div>
{% else %}
  {% comment %} Only show for products with booking configurations {% endcomment %}
{% endif %}

{% schema %}
{
  "name": "Booking Calendar",
  "target": "section",
  "enabled_on": {
    "templates": ["product"]
  },
  "settings": [
    {
      "type": "product",
      "id": "product",
      "label": "Product",
      "autofill": true
    },
    {
      "type": "color",
      "id": "primary_color",
      "label": "Primary Color",
      "default": "#007cba"
    },
    {
      "type": "color",
      "id": "accent_color",
      "label": "Accent Color",
      "default": "#28a745"
    },
    {
      "type": "checkbox",
      "id": "show_unavailable_dates",
      "label": "Show unavailable dates",
      "default": true
    }
  ]
}
{% endschema %}

