/* Booking Calendar Styles */
.booking-calendar-container {
  margin: 20px 0;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #ffffff;
  font-family:
    -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, sans-serif;
}

.booking-calendar-title {
  margin: 0 0 16px 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
}

.booking-calendar-wrapper {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.booking-calendar {
  border: 1px solid #ddd;
  border-radius: 6px;
  overflow: hidden;
  background: #fff;
}

.calendar-loading {
  padding: 40px;
  text-align: center;
  color: #666;
  font-style: italic;
}

/* Calendar Header */
.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: var(--primary-color, #007cba);
  color: white;
}

.calendar-nav-button {
  background: none;
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.calendar-nav-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.calendar-nav-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.calendar-month-year {
  font-size: 1.1rem;
  font-weight: 600;
}

/* Calendar Grid */
.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
}

.calendar-day-header {
  padding: 12px 8px;
  text-align: center;
  font-weight: 600;
  font-size: 0.875rem;
  color: #666;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
}

.calendar-day {
  padding: 12px 8px;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
  border-right: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.2s;
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.calendar-day:nth-child(7n) {
  border-right: none;
}

.calendar-day.other-month {
  color: #ccc;
  background-color: #fafafa;
}

.calendar-day.available {
  background-color: #fff;
  color: #333;
}

.calendar-day.available:hover {
  background-color: #e3f2fd;
  color: var(--primary-color, #007cba);
}

.calendar-day.unavailable {
  background-color: #f5f5f5;
  color: #999;
  cursor: not-allowed;
  text-decoration: line-through;
}

.calendar-day.selected {
  background-color: var(--accent-color, #28a745);
  color: white;
  font-weight: 600;
}

.calendar-day.today {
  font-weight: 600;
  position: relative;
}

.calendar-day.today::after {
  content: "";
  position: absolute;
  bottom: 2px;
  left: 50%;
  transform: translateX(-50%);
  width: 6px;
  height: 6px;
  background-color: var(--primary-color, #007cba);
  border-radius: 50%;
}

/* Booking Details */
.booking-details {
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
}

.selected-date {
  margin-bottom: 12px;
  font-size: 1rem;
}

.time-slots {
  margin-bottom: 12px;
}

.time-slots label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #333;
}

.time-slot-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  background-color: white;
}

.booking-duration,
.booking-price {
  margin-bottom: 8px;
  font-size: 0.9rem;
  color: #666;
}

.booking-price {
  font-weight: 600;
  color: var(--accent-color, #28a745);
}

/* Responsive Design */
@media (max-width: 768px) {
  .booking-calendar-container {
    margin: 16px 0;
    padding: 16px;
  }

  .calendar-day {
    padding: 8px 4px;
    font-size: 0.875rem;
    min-height: 36px;
  }

  .calendar-day-header {
    padding: 8px 4px;
    font-size: 0.75rem;
  }

  .calendar-header {
    padding: 12px;
  }

  .calendar-month-year {
    font-size: 1rem;
  }
}

/* Loading States */
.calendar-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.calendar-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid var(--primary-color, #007cba);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Error States */
.calendar-error {
  padding: 20px;
  text-align: center;
  color: #dc3545;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  margin: 10px 0;
}

/* Accessibility */
.calendar-day:focus {
  outline: 2px solid var(--primary-color, #007cba);
  outline-offset: -2px;
}

.time-slot-select:focus {
  outline: 2px solid var(--primary-color, #007cba);
  outline-offset: -2px;
}

/* Pricing Display */
.booking-pricing-display {
  margin-top: 16px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
}

.pricing-summary {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.total-price {
  font-size: 1.25rem;
  color: var(--accent-color, #28a745);
  text-align: center;
  padding: 12px;
  background-color: white;
  border-radius: 4px;
  border: 1px solid var(--accent-color, #28a745);
}

.price-adjustments {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.price-adjustment {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: white;
  border-radius: 4px;
  font-size: 0.9rem;
}

.price-adjustment.bulk_discount {
  border-left: 4px solid #28a745;
}

.price-adjustment.peak_pricing {
  border-left: 4px solid #ffc107;
}

.price-adjustment.seasonal_pricing {
  border-left: 4px solid #17a2b8;
}

.price-adjustment.base_override {
  border-left: 4px solid #6c757d;
}

.adjustment-name {
  font-weight: 500;
}

.adjustment-amount {
  font-weight: 600;
  color: #28a745;
}

.price-adjustment.peak_pricing .adjustment-amount,
.price-adjustment.seasonal_pricing .adjustment-amount {
  color: #dc3545;
}

.savings {
  text-align: center;
  font-weight: 600;
  color: #28a745;
  background-color: #d4edda;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #c3e6cb;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .calendar-day.available {
    border: 1px solid #000;
  }

  .calendar-day.selected {
    border: 2px solid #000;
  }

  .booking-pricing-display {
    border: 2px solid #000;
  }
}
