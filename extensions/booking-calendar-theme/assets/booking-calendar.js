/**
 * Booking Calendar JavaScript
 * Handles calendar rendering, date selection, and booking slot management
 */

class BookingCalendar {
  constructor(productId) {
    this.productId = productId;
    this.currentDate = new Date();
    this.selectedDate = null;
    this.availableSlots = new Map();
    this.bookingConfig = null;
    this.container = document.getElementById(`booking-calendar-${productId}`);
    this.detailsContainer = document.getElementById(
      `booking-details-${productId}`,
    );

    this.init();
  }

  async init() {
    try {
      await this.loadBookingConfig();
      await this.loadAvailableSlots();
      this.render();
      this.attachEventListeners();
    } catch (error) {
      console.error("Failed to initialize booking calendar:", error);
      this.showError("Failed to load booking calendar. Please try again.");
    }
  }

  async loadBookingConfig() {
    try {
      // Fetch booking configuration and slots from the app API
      const response = await fetch(
        `/apps/booky/api/booking-slots?productId=${this.productId}`,
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error);
      }

      this.bookingConfig = data.config;

      // Process the slots data
      this.processSlots(data.slots);
    } catch (error) {
      console.error("Error loading booking config:", error);
      // Fallback to mock configuration for development
      this.bookingConfig = {
        slotType: "HOURLY",
        slotDuration: 60,
        advanceBookingDays: 30,
        startTime: "09:00",
        endTime: "17:00",
        priceAdjustment: 0,
      };
      await this.loadMockSlots();
    }
  }

  processSlots(slots) {
    // Group slots by date
    const slotsByDate = new Map();

    slots.forEach((slot) => {
      if (!slotsByDate.has(slot.date)) {
        slotsByDate.set(slot.date, []);
      }
      slotsByDate.get(slot.date).push({
        time: slot.time,
        available: slot.available,
        duration: slot.duration,
      });
    });

    this.availableSlots = slotsByDate;
  }

  async loadAvailableSlots() {
    // This method is now handled by loadBookingConfig
    // Keeping for backward compatibility
  }

  async loadMockSlots() {
    // Fallback mock slots for development/testing
    const today = new Date();
    const endDate = new Date();
    endDate.setDate(
      today.getDate() + (this.bookingConfig.advanceBookingDays || 30),
    );

    for (let d = new Date(today); d <= endDate; d.setDate(d.getDate() + 1)) {
      const dateKey = this.formatDate(d);

      // Skip weekends for mock data
      if (d.getDay() === 0 || d.getDay() === 6) {
        continue;
      }

      if (this.bookingConfig.slotType === "HOURLY") {
        const slots = this.generateTimeSlots(d);
        this.availableSlots.set(dateKey, slots);
      } else {
        this.availableSlots.set(dateKey, [
          { time: "all-day", available: true },
        ]);
      }
    }
  }

  generateTimeSlots(date) {
    const slots = [];
    const [startHour, startMinute] = this.bookingConfig.startTime
      .split(":")
      .map(Number);
    const [endHour, endMinute] = this.bookingConfig.endTime
      .split(":")
      .map(Number);

    const slotDuration = this.bookingConfig.slotDuration;
    const current = new Date(date);
    current.setHours(startHour, startMinute, 0, 0);

    const end = new Date(date);
    end.setHours(endHour, endMinute, 0, 0);

    while (current < end) {
      const timeString = current.toLocaleTimeString("en-US", {
        hour: "2-digit",
        minute: "2-digit",
        hour12: false,
      });

      // Mock availability - in real implementation, check against existing bookings
      const available = Math.random() > 0.3; // 70% chance of being available

      slots.push({
        time: timeString,
        available: available,
      });

      current.setMinutes(current.getMinutes() + slotDuration);
    }

    return slots;
  }

  render() {
    const calendar = this.createCalendar();
    this.container.innerHTML = "";
    this.container.appendChild(calendar);
  }

  createCalendar() {
    const calendar = document.createElement("div");
    calendar.className = "calendar-widget";

    // Header
    const header = this.createHeader();
    calendar.appendChild(header);

    // Days of week
    const daysHeader = this.createDaysHeader();
    calendar.appendChild(daysHeader);

    // Calendar grid
    const grid = this.createCalendarGrid();
    calendar.appendChild(grid);

    return calendar;
  }

  createHeader() {
    const header = document.createElement("div");
    header.className = "calendar-header";

    const prevButton = document.createElement("button");
    prevButton.className = "calendar-nav-button";
    prevButton.innerHTML = "‹";
    prevButton.onclick = () => this.previousMonth();

    const monthYear = document.createElement("div");
    monthYear.className = "calendar-month-year";
    monthYear.textContent = this.currentDate.toLocaleDateString("en-US", {
      month: "long",
      year: "numeric",
    });

    const nextButton = document.createElement("button");
    nextButton.className = "calendar-nav-button";
    nextButton.innerHTML = "›";
    nextButton.onclick = () => this.nextMonth();

    header.appendChild(prevButton);
    header.appendChild(monthYear);
    header.appendChild(nextButton);

    return header;
  }

  createDaysHeader() {
    const daysHeader = document.createElement("div");
    daysHeader.className = "calendar-grid";

    const dayNames = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
    dayNames.forEach((day) => {
      const dayElement = document.createElement("div");
      dayElement.className = "calendar-day-header";
      dayElement.textContent = day;
      daysHeader.appendChild(dayElement);
    });

    return daysHeader;
  }

  createCalendarGrid() {
    const grid = document.createElement("div");
    grid.className = "calendar-grid";

    const firstDay = new Date(
      this.currentDate.getFullYear(),
      this.currentDate.getMonth(),
      1,
    );
    const lastDay = new Date(
      this.currentDate.getFullYear(),
      this.currentDate.getMonth() + 1,
      0,
    );
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    for (let i = 0; i < 42; i++) {
      const cellDate = new Date(startDate);
      cellDate.setDate(startDate.getDate() + i);

      const dayElement = this.createDayElement(
        cellDate,
        today,
        firstDay,
        lastDay,
      );
      grid.appendChild(dayElement);
    }

    return grid;
  }

  createDayElement(cellDate, today, firstDay, lastDay) {
    const dayElement = document.createElement("div");
    dayElement.className = "calendar-day";
    dayElement.textContent = cellDate.getDate();

    // Add classes based on date properties
    if (cellDate < firstDay || cellDate > lastDay) {
      dayElement.classList.add("other-month");
    }

    if (cellDate.getTime() === today.getTime()) {
      dayElement.classList.add("today");
    }

    const dateKey = this.formatDate(cellDate);
    const hasSlots = this.availableSlots.has(dateKey);

    if (cellDate >= today && hasSlots) {
      dayElement.classList.add("available");
      dayElement.onclick = () => this.selectDate(cellDate);
    } else if (cellDate >= today) {
      dayElement.classList.add("unavailable");
    }

    if (
      this.selectedDate &&
      cellDate.getTime() === this.selectedDate.getTime()
    ) {
      dayElement.classList.add("selected");
    }

    return dayElement;
  }

  selectDate(date) {
    this.selectedDate = new Date(date);
    const dateKey = this.formatDate(date);
    const slots = this.availableSlots.get(dateKey);

    // Update calendar display
    this.render();

    // Show booking details
    this.showBookingDetails(date, slots);

    // Update hidden form fields
    this.updateFormFields(date);

    // Calculate and display pricing
    this.updatePricing();
  }

  showBookingDetails(date, slots) {
    const dateDisplay = document.getElementById(
      `selected-date-display-${this.productId}`,
    );
    dateDisplay.textContent = date.toLocaleDateString("en-US", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    });

    // Populate time slots if hourly booking
    if (this.bookingConfig.slotType === "HOURLY") {
      const timeSelect = document.getElementById(`time-slot-${this.productId}`);
      timeSelect.innerHTML = '<option value="">Choose a time...</option>';

      slots.forEach((slot) => {
        if (slot.available) {
          const option = document.createElement("option");
          option.value = slot.time;
          option.textContent = slot.time;
          timeSelect.appendChild(option);
        }
      });

      timeSelect.onchange = () => {
        this.updateFormFields(date, timeSelect.value);
        this.updatePricing();
      };
    }

    this.detailsContainer.style.display = "block";
  }

  updateFormFields(date, time = "") {
    const dateField = document.getElementById(`booking-date-${this.productId}`);
    const timeField = document.getElementById(`booking-time-${this.productId}`);

    if (dateField) {
      dateField.value = this.formatDate(date);
    }

    if (timeField) {
      timeField.value = time;
    }
  }

  previousMonth() {
    this.currentDate.setMonth(this.currentDate.getMonth() - 1);
    this.render();
  }

  nextMonth() {
    this.currentDate.setMonth(this.currentDate.getMonth() + 1);
    this.render();
  }

  formatDate(date) {
    return date.toISOString().split("T")[0];
  }

  showError(message) {
    this.container.innerHTML = `<div class="calendar-error">${message}</div>`;
  }

  async updatePricing() {
    if (!this.selectedDate || !this.bookingConfig.enableDynamicPricing) {
      return;
    }

    try {
      // Get selected time if hourly booking
      const timeSelect = document.getElementById(`time-slot-${this.productId}`);
      const selectedTime = timeSelect ? timeSelect.value : null;

      if (this.bookingConfig.slotType === "HOURLY" && !selectedTime) {
        return; // Wait for time selection
      }

      // Create slots array for pricing calculation
      const slots = [
        {
          date: this.formatDate(this.selectedDate),
          time: selectedTime,
          duration: this.bookingConfig.slotDuration,
        },
      ];

      // Get base product price (you might need to pass this from the product page)
      const basePrice = this.getBaseProductPrice();

      // Calculate pricing
      const response = await fetch("/apps/booky/api/booking-pricing", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          productId: this.productId,
          slots: slots,
          basePrice: basePrice,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        this.displayPricing(data.pricing);
        this.updatePricingFormFields(data.pricing);
      }
    } catch (error) {
      console.error("Error calculating pricing:", error);
    }
  }

  getBaseProductPrice() {
    // Try to get price from product page context
    // This is a simplified implementation - in a real scenario,
    // you'd get this from the product data or page context
    const priceElement = document.querySelector(
      ".price, [data-price], .product-price",
    );
    if (priceElement) {
      const priceText = priceElement.textContent || priceElement.dataset.price;
      const price = parseFloat(priceText.replace(/[^0-9.]/g, ""));
      return isNaN(price) ? 100 : price; // Default to 100 if can't parse
    }
    return 100; // Default price
  }

  displayPricing(pricing) {
    // Find or create pricing display container
    let pricingContainer = document.getElementById(
      `pricing-display-${this.productId}`,
    );

    if (!pricingContainer) {
      pricingContainer = document.createElement("div");
      pricingContainer.id = `pricing-display-${this.productId}`;
      pricingContainer.className = "booking-pricing-display";

      // Insert after booking details
      const detailsContainer = this.detailsContainer;
      if (detailsContainer && detailsContainer.parentNode) {
        detailsContainer.parentNode.insertBefore(
          pricingContainer,
          detailsContainer.nextSibling,
        );
      }
    }

    // Build pricing display HTML
    let html = `
      <div class="pricing-summary">
        <div class="total-price">
          <strong>Total: $${pricing.totalPrice.toFixed(2)}</strong>
        </div>
    `;

    if (pricing.adjustments && pricing.adjustments.length > 0) {
      html += '<div class="price-adjustments">';
      pricing.adjustments.forEach((adjustment) => {
        const sign = adjustment.amount >= 0 ? "+" : "";
        html += `
          <div class="price-adjustment ${adjustment.type}">
            <span class="adjustment-name">${adjustment.name}</span>
            <span class="adjustment-amount">${sign}$${adjustment.amount.toFixed(2)}</span>
          </div>
        `;
      });
      html += "</div>";
    }

    if (pricing.breakdown && pricing.breakdown.savings > 0) {
      html += `
        <div class="savings">
          You save: $${pricing.breakdown.savings.toFixed(2)}
        </div>
      `;
    }

    html += "</div>";
    pricingContainer.innerHTML = html;
    pricingContainer.style.display = "block";
  }

  updatePricingFormFields(pricing) {
    // Update hidden form fields with pricing information
    const totalPriceField = document.getElementById(
      `booking-total-price-${this.productId}`,
    );
    const adjustmentsField = document.getElementById(
      `booking-price-adjustments-${this.productId}`,
    );
    const originalPriceField = document.getElementById(
      `booking-original-price-${this.productId}`,
    );

    if (totalPriceField) {
      totalPriceField.value = pricing.totalPrice.toFixed(2);
    }

    if (adjustmentsField && pricing.adjustments) {
      adjustmentsField.value = JSON.stringify(pricing.adjustments);
    }

    if (originalPriceField && pricing.breakdown) {
      originalPriceField.value = pricing.breakdown.originalPrice.toFixed(2);
    }
  }

  attachEventListeners() {
    // Add any additional event listeners here
  }
}

// Initialize booking calendars when DOM is ready
document.addEventListener("DOMContentLoaded", function () {
  // Find all booking calendar containers and initialize them
  const containers = document.querySelectorAll(".booking-calendar-container");

  containers.forEach((container) => {
    const productId = container.dataset.productId;
    if (productId) {
      new BookingCalendar(productId);
    }
  });
});

// Export for potential external use
window.BookingCalendar = BookingCalendar;
