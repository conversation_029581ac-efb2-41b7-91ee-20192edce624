{% comment %}
  Booking Calendar Helper Snippets
  Contains reusable Liquid code for booking functionality
{% endcomment %}

{% comment %}
  Snippet: booking_config_check
  Usage: {% render 'booking-helpers', type: 'config_check', product: product %}
  Returns: true if product has booking configuration
{% endcomment %}
{% if type == 'config_check' %}
  {% assign has_booking_config = false %}
  {% if product.metafields.booky.booking_config.value %}
    {% assign has_booking_config = true %}
  {% endif %}
  {{ has_booking_config }}
{% endif %}

{% comment %}
  Snippet: booking_price_display
  Usage: {% render 'booking-helpers', type: 'price_display', config: booking_config %}
  Returns: formatted price adjustment display
{% endcomment %}
{% if type == 'price_display' %}
  {% if config.price_adjustment and config.price_adjustment > 0 %}
    <span class="booking-price-adjustment">
      + {{ config.price_adjustment | money }}
    </span>
  {% endif %}
{% endif %}

{% comment %}
  Snippet: booking_duration_display
  Usage: {% render 'booking-helpers', type: 'duration_display', config: booking_config %}
  Returns: formatted duration display
{% endcomment %}
{% if type == 'duration_display' %}
  {% if config.slot_duration %}
    <span class="booking-duration-display">
      {{ config.slot_duration }} {{ 'booking.calendar.minutes' | t }}
    </span>
  {% endif %}
{% endif %}

{% comment %}
  Snippet: booking_type_display
  Usage: {% render 'booking-helpers', type: 'type_display', config: booking_config %}
  Returns: formatted booking type display
{% endcomment %}
{% if type == 'type_display' %}
  {% if config.slot_type %}
    <span class="booking-type-display">
      {% if config.slot_type == 'HOURLY' %}
        {{ 'booking.calendar.hourly_booking' | t: default: 'Hourly Booking' }}
      {% elsif config.slot_type == 'DAILY' %}
        {{ 'booking.calendar.daily_booking' | t: default: 'Daily Booking' }}
      {% endif %}
    </span>
  {% endif %}
{% endif %}

{% comment %}
  Snippet: booking_availability_indicator
  Usage: {% render 'booking-helpers', type: 'availability', product: product %}
  Returns: availability indicator for product
{% endcomment %}
{% if type == 'availability' %}
  {% assign booking_config = product.metafields.booky.booking_config.value %}
  {% if booking_config %}
    <div class="booking-availability-indicator">
      <span class="booking-available-icon">📅</span>
      <span class="booking-available-text">
        {{ 'booking.calendar.booking_available' | t: default: 'Booking Available' }}
      </span>
    </div>
  {% endif %}
{% endif %}

{% comment %}
  Snippet: booking_form_integration
  Usage: {% render 'booking-helpers', type: 'form_integration', product: product %}
  Returns: hidden form fields for cart integration
{% endcomment %}
{% if type == 'form_integration' %}
  {% assign booking_config = product.metafields.booky.booking_config.value %}
  {% if booking_config %}
    <!-- Booking form integration fields -->
    <input type="hidden" name="properties[_booking_enabled]" value="true">
    <input type="hidden" name="properties[_booking_config_id]" value="{{ booking_config.id }}">
    <input type="hidden" name="properties[_booking_slot_type]" value="{{ booking_config.slot_type }}">
    <input type="hidden" name="properties[_booking_duration]" value="{{ booking_config.slot_duration }}">
    
    {% if booking_config.price_adjustment and booking_config.price_adjustment > 0 %}
      <input type="hidden" name="properties[_booking_price_adjustment]" value="{{ booking_config.price_adjustment }}">
    {% endif %}
  {% endif %}
{% endif %}
