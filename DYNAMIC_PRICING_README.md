# Dynamic Pricing for Booking System

## Overview

The Booky app now supports dynamic pricing for bookings based on various factors including booking range, peak times, and seasonal rates. This feature allows merchants to maximize revenue by applying intelligent pricing strategies.

## Features Implemented

### 1. **Database Schema Updates**
- Extended `BookingConfiguration` model with dynamic pricing fields
- Added support for JSON-based pricing rules for flexibility
- Migration: `20250930082809_add_dynamic_pricing`

### 2. **Pricing Service (`app/services/pricing.server.ts`)**
- Comprehensive `BookingPricingService` class
- Supports multiple pricing strategies:
  - **Base Price Override**: Custom base pricing for bookings
  - **Bulk Discounts**: Percentage discounts for multiple slots
  - **Peak Pricing**: Higher rates for weekends, specific times, or date ranges
  - **Seasonal Pricing**: Special rates for holidays or events

### 3. **Admin UI Updates**
- Enhanced booking configuration page (`app/routes/app.bookings.$id.tsx`)
- Dynamic pricing configuration form with:
  - Enable/disable dynamic pricing
  - Bulk discount tier configuration
  - Peak pricing rules setup
  - Seasonal pricing rules setup

### 4. **API Endpoints**
- **`/api/booking-pricing`**: Calculate pricing for selected slots
- **`/api/pricing-test`**: Test endpoint for pricing validation
- Updated **`/api/booking-slots`**: Include pricing configuration

### 5. **Theme App Extension Updates**
- Real-time pricing calculation in booking calendar
- Visual pricing display with breakdown
- Cart integration with pricing information
- Updated CSS for pricing display components

## Pricing Calculation Logic

### Base Pricing
```
Base Price = basePriceOverride || productPrice
Per Slot Price = pricePerSlot || 0
Original Price = Base Price + (Per Slot Price × Number of Slots)
```

### Bulk Discounts
Applied when `bulkDiscountEnabled` is true:
```json
[
  {"minSlots": 3, "discountPercent": 10, "name": "3+ Slots Discount"},
  {"minSlots": 5, "discountPercent": 20, "name": "5+ Slots Discount"}
]
```

### Peak Pricing
Applied when `peakPricingEnabled` is true:
```json
[
  {"type": "weekday", "days": [6,7], "multiplier": 1.5, "name": "Weekend Premium"},
  {"type": "time", "startTime": "18:00", "endTime": "22:00", "multiplier": 1.2, "name": "Evening Premium"},
  {"type": "date", "startDate": "2024-07-01", "endDate": "2024-07-31", "multiplier": 1.3, "name": "Summer Peak"}
]
```

### Seasonal Pricing
Special rates for specific periods:
```json
[
  {"startDate": "2024-12-20", "endDate": "2024-12-31", "multiplier": 2.0, "name": "Holiday Season"},
  {"startDate": "2024-07-04", "endDate": "2024-07-04", "multiplier": 1.8, "name": "Independence Day"}
]
```

## Usage Examples

### Test Results
The pricing service has been tested with the following scenarios:

1. **Single slot - weekday morning**: $60 (base price override applied)
2. **3 slots - bulk discount**: $72 (10% bulk discount applied, saving $8)
3. **Weekend slot - peak pricing**: $90 (50% weekend premium applied)
4. **5 slots - maximum bulk discount**: $80 (20% bulk discount applied, saving $20)

## Cart Integration

When customers select booking slots, the following information is added to cart line item properties:
- `Booking Date`: Selected date
- `Booking Time`: Selected time (for hourly bookings)
- `Booking Duration`: Slot duration
- `Booking Total Price`: Calculated total price
- `Booking Price Adjustments`: JSON array of applied adjustments
- `Booking Original Price`: Original price before adjustments

## Configuration

### Merchant Setup
1. Navigate to **Booking Management** in the app admin
2. Edit a booking configuration
3. Enable **Dynamic Pricing**
4. Configure desired pricing rules:
   - **Bulk Discounts**: Set minimum slots and discount percentages
   - **Peak Pricing**: Define peak times, days, or date ranges
   - **Seasonal Pricing**: Set special rates for holidays or events

### JSON Configuration Examples

**Bulk Discount Tiers:**
```json
[
  {"minSlots": 3, "discountPercent": 10, "name": "3+ Slots Discount"},
  {"minSlots": 5, "discountPercent": 20, "name": "5+ Slots Discount"},
  {"minSlots": 10, "discountPercent": 30, "name": "10+ Slots Discount"}
]
```

**Peak Pricing Rules:**
```json
[
  {"type": "weekday", "days": [6,7], "multiplier": 1.5, "name": "Weekend Premium"},
  {"type": "time", "startTime": "18:00", "endTime": "22:00", "multiplier": 1.2, "name": "Evening Premium"}
]
```

## Technical Implementation

### Files Modified/Created
- `prisma/schema.prisma` - Database schema updates
- `app/services/pricing.server.ts` - Pricing calculation service
- `app/routes/app.bookings.$id.tsx` - Admin UI for pricing configuration
- `app/routes/api.booking-pricing.tsx` - Pricing calculation API
- `extensions/booking-calendar-theme/assets/booking-calendar.js` - Frontend pricing integration
- `extensions/booking-calendar-theme/assets/booking-calendar.css` - Pricing display styles
- `extensions/booking-calendar-theme/blocks/booking_calendar.liquid` - Cart integration

### Key Classes and Methods
- `BookingPricingService.calculateBookingPrice()` - Main pricing calculation
- `BookingPricingService.calculateBulkDiscount()` - Bulk discount logic
- `BookingPricingService.calculatePeakPricing()` - Peak pricing logic
- `BookingPricingService.calculateSeasonalPricing()` - Seasonal pricing logic

## Future Enhancements

1. **Advanced Peak Pricing**: Support for multiple time ranges per day
2. **Dynamic Base Pricing**: Adjust base prices based on demand
3. **Group Discounts**: Different pricing for different customer groups
4. **Promotional Codes**: Integration with discount codes
5. **Analytics**: Pricing performance tracking and optimization suggestions

## Testing

The pricing service includes comprehensive test coverage and can be validated using the `/api/pricing-test` endpoint which provides real-time pricing calculations for various scenarios.
