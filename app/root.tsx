import { Links, Meta, <PERSON>let, <PERSON><PERSON><PERSON>, ScrollRestoration } from "react-router";

export default function App() {
  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width,initial-scale=1" />
        {/* Security headers for embedded Shopify apps */}
        <meta
          httpEquiv="Content-Security-Policy"
          content="frame-ancestors https://*.myshopify.com https://admin.shopify.com;"
        />
        <meta httpEquiv="X-Frame-Options" content="ALLOWALL" />
        <link rel="preconnect" href="https://cdn.shopify.com/" />
        <link
          rel="stylesheet"
          href="https://cdn.shopify.com/static/fonts/inter/v4/styles.css"
        />
        <Meta />
        <Links />
      </head>
      <body>
        <Outlet />
        <ScrollRestoration />
        <Scripts />
      </body>
    </html>
  );
}
