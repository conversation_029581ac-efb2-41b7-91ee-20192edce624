/**
 * Dynamic Pricing Service for Booking System
 * Handles calculation of booking prices based on various factors
 */

import type { BookingConfiguration } from "@prisma/client";

export interface BookingSlot {
  date: string;
  time?: string;
  duration: number;
}

export interface PricingResult {
  basePrice: number;
  totalPrice: number;
  pricePerSlot: number;
  adjustments: PriceAdjustment[];
  breakdown: PriceBreakdown;
}

export interface PriceAdjustment {
  type: "bulk_discount" | "peak_pricing" | "seasonal_pricing" | "base_override";
  name: string;
  amount: number;
  percentage?: number;
  description: string;
}

export interface PriceBreakdown {
  originalPrice: number;
  baseAdjustment: number;
  bulkDiscount: number;
  peakPricing: number;
  seasonalPricing: number;
  finalPrice: number;
  savings: number;
}

export interface BulkDiscountTier {
  minSlots: number;
  discountPercent: number;
  name?: string;
}

export interface PeakPricingRule {
  type: "weekday" | "time" | "date";
  days?: number[]; // 1-7 (Monday-Sunday)
  startTime?: string;
  endTime?: string;
  startDate?: string;
  endDate?: string;
  multiplier: number;
  name?: string;
}

export interface SeasonalPricingRule {
  startDate: string;
  endDate: string;
  multiplier: number;
  name: string;
}

export class BookingPricingService {
  /**
   * Calculate the total price for a booking based on slots and configuration
   */
  static calculateBookingPrice(
    config: BookingConfiguration,
    slots: BookingSlot[],
    baseProductPrice: number = 0,
  ): PricingResult {
    const numSlots = slots.length;
    const adjustments: PriceAdjustment[] = [];

    // Determine base price
    let basePrice = config.basePriceOverride || baseProductPrice;
    let pricePerSlot = config.pricePerSlot || 0;

    if (config.basePriceOverride) {
      adjustments.push({
        type: "base_override",
        name: "Base Price Override",
        amount: config.basePriceOverride - baseProductPrice,
        description: `Custom booking price: $${config.basePriceOverride.toFixed(2)}`,
      });
    }

    // Calculate original total price
    const originalPrice = basePrice + pricePerSlot * numSlots;
    let currentPrice = originalPrice;

    // Apply dynamic pricing if enabled
    if (config.enableDynamicPricing) {
      // 1. Apply bulk discounts
      if (config.bulkDiscountEnabled) {
        const bulkDiscount = this.calculateBulkDiscount(
          config,
          numSlots,
          currentPrice,
        );
        if (bulkDiscount.amount > 0) {
          adjustments.push(bulkDiscount);
          currentPrice -= bulkDiscount.amount;
        }
      }

      // 2. Apply peak pricing
      if (config.peakPricingEnabled) {
        const peakAdjustments = this.calculatePeakPricing(
          config,
          slots,
          currentPrice,
        );
        peakAdjustments.forEach((adj) => {
          adjustments.push(adj);
          currentPrice += adj.amount;
        });
      }

      // 3. Apply seasonal pricing
      const seasonalAdjustments = this.calculateSeasonalPricing(
        config,
        slots,
        currentPrice,
      );
      seasonalAdjustments.forEach((adj) => {
        adjustments.push(adj);
        currentPrice += adj.amount;
      });
    }

    // Calculate breakdown
    const breakdown: PriceBreakdown = {
      originalPrice,
      baseAdjustment: config.basePriceOverride
        ? config.basePriceOverride - baseProductPrice
        : 0,
      bulkDiscount: adjustments
        .filter((a) => a.type === "bulk_discount")
        .reduce((sum, a) => sum + a.amount, 0),
      peakPricing: adjustments
        .filter((a) => a.type === "peak_pricing")
        .reduce((sum, a) => sum + a.amount, 0),
      seasonalPricing: adjustments
        .filter((a) => a.type === "seasonal_pricing")
        .reduce((sum, a) => sum + a.amount, 0),
      finalPrice: currentPrice,
      savings: originalPrice - currentPrice,
    };

    return {
      basePrice,
      totalPrice: Math.max(0, currentPrice), // Ensure price is never negative
      pricePerSlot,
      adjustments,
      breakdown,
    };
  }

  /**
   * Calculate bulk discount based on number of slots
   */
  private static calculateBulkDiscount(
    config: BookingConfiguration,
    numSlots: number,
    currentPrice: number,
  ): PriceAdjustment {
    try {
      const tiers: BulkDiscountTier[] = JSON.parse(
        config.bulkDiscountTiers || "[]",
      );

      // Find the highest applicable tier
      const applicableTier = tiers
        .filter((tier) => numSlots >= tier.minSlots)
        .sort((a, b) => b.discountPercent - a.discountPercent)[0];

      if (applicableTier) {
        const discountAmount =
          (currentPrice * applicableTier.discountPercent) / 100;
        return {
          type: "bulk_discount",
          name:
            applicableTier.name ||
            `${applicableTier.discountPercent}% Bulk Discount`,
          amount: discountAmount,
          percentage: applicableTier.discountPercent,
          description: `${applicableTier.discountPercent}% discount for booking ${numSlots} slots`,
        };
      }
    } catch (error) {
      console.error("Error parsing bulk discount tiers:", error);
    }

    return {
      type: "bulk_discount",
      name: "No Bulk Discount",
      amount: 0,
      description: "No bulk discount applied",
    };
  }

  /**
   * Calculate peak pricing adjustments
   */
  private static calculatePeakPricing(
    config: BookingConfiguration,
    slots: BookingSlot[],
    currentPrice: number,
  ): PriceAdjustment[] {
    const adjustments: PriceAdjustment[] = [];

    try {
      const rules: PeakPricingRule[] = JSON.parse(
        config.peakPricingRules || "[]",
      );

      for (const slot of slots) {
        const slotDate = new Date(slot.date);
        const dayOfWeek = slotDate.getDay() || 7; // Convert Sunday (0) to 7

        for (const rule of rules) {
          let applies = false;
          let ruleName = rule.name || "Peak Pricing";

          switch (rule.type) {
            case "weekday":
              applies = rule.days?.includes(dayOfWeek) || false;
              break;
            case "time":
              if (slot.time && rule.startTime && rule.endTime) {
                const slotTime = slot.time;
                applies =
                  slotTime >= rule.startTime && slotTime <= rule.endTime;
              }
              break;
            case "date":
              if (rule.startDate && rule.endDate) {
                const ruleStart = new Date(rule.startDate);
                const ruleEnd = new Date(rule.endDate);
                applies = slotDate >= ruleStart && slotDate <= ruleEnd;
              }
              break;
          }

          if (applies) {
            const pricePerSlotAdjustment =
              (currentPrice / slots.length) * (rule.multiplier - 1);
            adjustments.push({
              type: "peak_pricing",
              name: ruleName,
              amount: pricePerSlotAdjustment,
              description: `${((rule.multiplier - 1) * 100).toFixed(0)}% peak pricing for ${slot.date}${slot.time ? ` at ${slot.time}` : ""}`,
            });
          }
        }
      }
    } catch (error) {
      console.error("Error parsing peak pricing rules:", error);
    }

    return adjustments;
  }

  /**
   * Calculate seasonal pricing adjustments
   */
  private static calculateSeasonalPricing(
    config: BookingConfiguration,
    slots: BookingSlot[],
    currentPrice: number,
  ): PriceAdjustment[] {
    const adjustments: PriceAdjustment[] = [];

    try {
      const rules: SeasonalPricingRule[] = JSON.parse(
        config.seasonalPricingRules || "[]",
      );

      for (const slot of slots) {
        const slotDate = new Date(slot.date);

        for (const rule of rules) {
          const ruleStart = new Date(rule.startDate);
          const ruleEnd = new Date(rule.endDate);

          if (slotDate >= ruleStart && slotDate <= ruleEnd) {
            const pricePerSlotAdjustment =
              (currentPrice / slots.length) * (rule.multiplier - 1);
            adjustments.push({
              type: "seasonal_pricing",
              name: rule.name,
              amount: pricePerSlotAdjustment,
              description: `${((rule.multiplier - 1) * 100).toFixed(0)}% seasonal pricing for ${rule.name}`,
            });
          }
        }
      }
    } catch (error) {
      console.error("Error parsing seasonal pricing rules:", error);
    }

    return adjustments;
  }

  /**
   * Get pricing preview for a configuration
   */
  static getPricingPreview(
    config: BookingConfiguration,
    baseProductPrice: number = 100,
  ): {
    examples: Array<{
      slots: number;
      description: string;
      pricing: PricingResult;
    }>;
  } {
    const examples = [1, 3, 5, 10].map((numSlots) => {
      const mockSlots: BookingSlot[] = Array.from(
        { length: numSlots },
        (_, i) => ({
          date: new Date(Date.now() + i * 24 * 60 * 60 * 1000)
            .toISOString()
            .split("T")[0],
          time: config.slotType === "HOURLY" ? "10:00" : undefined,
          duration: config.slotDuration,
        }),
      );

      return {
        slots: numSlots,
        description: `${numSlots} slot${numSlots > 1 ? "s" : ""}`,
        pricing: this.calculateBookingPrice(
          config,
          mockSlots,
          baseProductPrice,
        ),
      };
    });

    return { examples };
  }
}
