/**
 * Configuration validation utilities for Shopify app
 */

export interface AppConfig {
  apiKey: string;
  apiSecret: string;
  appUrl: string;
  scopes: string[];
  databaseUrl: string;
}

export function validateEnvironmentVariables(): AppConfig {
  const requiredEnvVars = [
    'SHOPIFY_API_KEY',
    'SHOPIFY_API_SECRET', 
    'SHOPIFY_APP_URL',
    'SCOPES'
  ];

  const missing = requiredEnvVars.filter(envVar => !process.env[envVar]);
  
  if (missing.length > 0) {
    throw new Error(
      `Missing required environment variables: ${missing.join(', ')}\n` +
      'Please check your .env file and ensure all required variables are set.'
    );
  }

  const config: AppConfig = {
    apiKey: process.env.SHOPIFY_API_KEY!,
    apiSecret: process.env.SHOPIFY_API_SECRET!,
    appUrl: process.env.SHOPIFY_APP_URL!,
    scopes: process.env.SCOPES!.split(',').map(scope => scope.trim()),
    databaseUrl: process.env.DATABASE_URL || 'file:./dev.sqlite'
  };

  // Validate API key format
  if (!/^[a-f0-9]{32}$/.test(config.apiKey)) {
    console.warn('API key format may be invalid. Expected 32-character hex string.');
  }

  // Validate app URL format
  try {
    new URL(config.appUrl);
  } catch {
    throw new Error(`Invalid SHOPIFY_APP_URL format: ${config.appUrl}`);
  }

  // Validate scopes
  const validScopes = [
    'read_products', 'write_products',
    'read_orders', 'write_orders',
    'read_customers', 'write_customers',
    'read_inventory', 'write_inventory',
    'read_fulfillments', 'write_fulfillments',
    'read_shipping', 'write_shipping',
    'read_analytics', 'read_reports',
    'read_price_rules', 'write_price_rules',
    'read_discounts', 'write_discounts',
    'read_marketing_events', 'write_marketing_events',
    'read_resource_feedbacks', 'write_resource_feedbacks',
    'read_shopify_payments_payouts', 'read_shopify_payments_disputes',
    'read_translations', 'write_translations',
    'read_locations', 'read_script_tags', 'write_script_tags',
    'read_checkouts', 'write_checkouts',
    'read_assigned_fulfillment_orders', 'write_assigned_fulfillment_orders',
    'read_merchant_managed_fulfillment_orders', 'write_merchant_managed_fulfillment_orders',
    'read_third_party_fulfillment_orders', 'write_third_party_fulfillment_orders'
  ];

  const invalidScopes = config.scopes.filter(scope => !validScopes.includes(scope));
  if (invalidScopes.length > 0) {
    console.warn(`Potentially invalid scopes detected: ${invalidScopes.join(', ')}`);
  }

  return config;
}

export function logConfigurationStatus() {
  try {
    const config = validateEnvironmentVariables();
    console.log('✅ Configuration validation passed');
    console.log(`📱 App URL: ${config.appUrl}`);
    console.log(`🔑 API Key: ${config.apiKey.substring(0, 8)}...`);
    console.log(`🔐 Scopes: ${config.scopes.join(', ')}`);
    console.log(`💾 Database: ${config.databaseUrl}`);
  } catch (error) {
    console.error('❌ Configuration validation failed:', error);
    throw error;
  }
}
