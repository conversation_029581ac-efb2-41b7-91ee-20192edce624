import type { ActionFunctionArgs } from "react-router";
import { authenticate } from "../shopify.server";
import db from "../db.server";

export const action = async ({ request }: ActionFunctionArgs) => {
  try {
    const { shop, session, topic } = await authenticate.webhook(request);

    console.log(`Received ${topic} webhook for ${shop}`);

    // Webhook requests can trigger multiple times and after an app has already been uninstalled.
    // If this webhook already ran, the session may have been deleted previously.
    if (session) {
      await db.session.deleteMany({ where: { shop } });
      console.log(`Cleaned up sessions for shop: ${shop}`);

      // Clean up any app-specific data for the shop
      await db.bookingConfiguration.deleteMany({ where: { shop } });
      await db.booking.deleteMany({ where: { shop } });
      console.log(`Cleaned up booking data for shop: ${shop}`);
    }

    return new Response(null, { status: 200 });
  } catch (error) {
    console.error("Error processing app uninstalled webhook:", error);
    return new Response(null, { status: 500 });
  }
};
