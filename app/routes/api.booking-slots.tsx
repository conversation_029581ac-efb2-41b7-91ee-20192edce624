import type { LoaderFunctionArgs } from "react-router";
import { authenticate } from "../shopify.server";
import prisma from "../db.server";
import { BookingPricingService } from "../services/pricing.server";

export async function loader({ request }: LoaderFunctionArgs) {
  const { session } = await authenticate.public.appProxy(request);

  if (!session) {
    return new Response(JSON.stringify({ error: "Unauthorized" }), {
      status: 401,
      headers: { "Content-Type": "application/json" },
    });
  }

  const url = new URL(request.url);
  const productId = url.searchParams.get("productId");
  const startDate = url.searchParams.get("startDate");
  const endDate = url.searchParams.get("endDate");

  if (!productId) {
    return new Response(JSON.stringify({ error: "Product ID is required" }), {
      status: 400,
      headers: { "Content-Type": "application/json" },
    });
  }

  try {
    // Get booking configuration for the product
    const bookingConfig = await prisma.bookingConfiguration.findFirst({
      where: {
        productId: productId,
        isActive: true,
      },
    });

    if (!bookingConfig) {
      return new Response(
        JSON.stringify({
          slots: [],
          message: "No booking configuration found for this product",
        }),
        {
          headers: { "Content-Type": "application/json" },
        },
      );
    }

    // Calculate date range
    const start = startDate ? new Date(startDate) : new Date();
    const end = endDate
      ? new Date(endDate)
      : new Date(
          Date.now() + bookingConfig.advanceBookingDays * 24 * 60 * 60 * 1000,
        );

    // Generate available slots
    const slots = await generateAvailableSlots(bookingConfig, start, end);

    return new Response(
      JSON.stringify({
        slots,
        config: {
          slotType: bookingConfig.slotType,
          slotDuration: bookingConfig.slotDuration,
          advanceBookingDays: bookingConfig.advanceBookingDays,
          startTime: bookingConfig.startTime,
          endTime: bookingConfig.endTime,
          basePriceOverride: bookingConfig.basePriceOverride,
          pricePerSlot: bookingConfig.pricePerSlot,
          enableDynamicPricing: bookingConfig.enableDynamicPricing,
          bulkDiscountEnabled: bookingConfig.bulkDiscountEnabled,
          peakPricingEnabled: bookingConfig.peakPricingEnabled,
          bulkDiscountTiers: bookingConfig.bulkDiscountTiers,
          peakPricingRules: bookingConfig.peakPricingRules,
          seasonalPricingRules: bookingConfig.seasonalPricingRules,
        },
      }),
      {
        headers: { "Content-Type": "application/json" },
      },
    );
  } catch (error) {
    console.error("Error fetching booking slots:", error);
    return new Response(
      JSON.stringify({ error: "Failed to fetch booking slots" }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" },
      },
    );
  }
}

async function generateAvailableSlots(
  config: any,
  startDate: Date,
  endDate: Date,
) {
  const slots = [];
  const currentDate = new Date(startDate);

  // Get existing bookings in the date range
  const existingBookings = await prisma.booking.findMany({
    where: {
      bookingConfigurationId: config.id,
      status: {
        in: ["CONFIRMED", "PENDING"],
      },
      bookingDate: {
        gte: startDate,
        lte: endDate,
      },
    },
  });

  // Create a map of booked slots for quick lookup
  const bookedSlots = new Map();
  existingBookings.forEach((booking) => {
    const dateKey = booking.bookingDate.toISOString().split("T")[0];
    const timeKey = booking.bookingTime || "all-day";
    const key = `${dateKey}-${timeKey}`;
    bookedSlots.set(key, true);
  });

  while (currentDate <= endDate) {
    const dateKey = currentDate.toISOString().split("T")[0];

    // Skip past dates
    if (currentDate < new Date()) {
      currentDate.setDate(currentDate.getDate() + 1);
      continue;
    }

    // Skip excluded days (weekends by default)
    const dayOfWeek = currentDate.getDay();
    if (config.excludedDays && config.excludedDays.includes(dayOfWeek)) {
      currentDate.setDate(currentDate.getDate() + 1);
      continue;
    }

    if (config.slotType === "HOURLY") {
      // Generate hourly slots
      const timeSlots = generateTimeSlots(config, currentDate);

      timeSlots.forEach((timeSlot) => {
        const slotKey = `${dateKey}-${timeSlot.time}`;
        const isBooked = bookedSlots.has(slotKey);

        slots.push({
          date: dateKey,
          time: timeSlot.time,
          available: !isBooked,
          duration: config.slotDuration,
        });
      });
    } else {
      // Daily booking
      const slotKey = `${dateKey}-all-day`;
      const isBooked = bookedSlots.has(slotKey);

      slots.push({
        date: dateKey,
        time: "all-day",
        available: !isBooked,
        duration: config.slotDuration,
      });
    }

    currentDate.setDate(currentDate.getDate() + 1);
  }

  return slots;
}

function generateTimeSlots(config: any, date: Date) {
  const slots = [];
  const [startHour, startMinute] = (config.startTime || "09:00")
    .split(":")
    .map(Number);
  const [endHour, endMinute] = (config.endTime || "17:00")
    .split(":")
    .map(Number);

  const slotDuration = config.slotDuration || 60;
  const current = new Date(date);
  current.setHours(startHour, startMinute, 0, 0);

  const end = new Date(date);
  end.setHours(endHour, endMinute, 0, 0);

  while (current < end) {
    const timeString = current.toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: false,
    });

    slots.push({
      time: timeString,
    });

    current.setMinutes(current.getMinutes() + slotDuration);
  }

  return slots;
}

export async function action({ request }: LoaderFunctionArgs) {
  const { session } = await authenticate.public.appProxy(request);

  if (!session) {
    return new Response(JSON.stringify({ error: "Unauthorized" }), {
      status: 401,
      headers: { "Content-Type": "application/json" },
    });
  }

  if (request.method === "POST") {
    try {
      const body = await request.json();
      const { productId, date, time, customerInfo } = body;

      if (!productId || !date) {
        return new Response(
          JSON.stringify({ error: "Product ID and date are required" }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          },
        );
      }

      // Get booking configuration
      const bookingConfig = await prisma.bookingConfiguration.findFirst({
        where: {
          productId: productId,
          isActive: true,
        },
      });

      if (!bookingConfig) {
        return new Response(
          JSON.stringify({ error: "No booking configuration found" }),
          {
            status: 404,
            headers: { "Content-Type": "application/json" },
          },
        );
      }

      // Check if slot is available
      const existingBooking = await prisma.booking.findFirst({
        where: {
          bookingConfigurationId: bookingConfig.id,
          bookingDate: new Date(date),
          bookingTime: time || null,
          status: {
            in: ["CONFIRMED", "PENDING"],
          },
        },
      });

      if (existingBooking) {
        return new Response(
          JSON.stringify({ error: "This time slot is already booked" }),
          {
            status: 409,
            headers: { "Content-Type": "application/json" },
          },
        );
      }

      // Create the booking
      const booking = await prisma.booking.create({
        data: {
          bookingConfigurationId: bookingConfig.id,
          bookingDate: new Date(date),
          bookingTime: time || null,
          customerEmail: customerInfo?.email || "",
          customerName: customerInfo?.name || "",
          customerPhone: customerInfo?.phone || "",
          status: "PENDING",
          totalPrice: bookingConfig.priceAdjustment || 0,
        },
      });

      return new Response(
        JSON.stringify({
          success: true,
          bookingId: booking.id,
          message: "Booking created successfully",
        }),
        {
          headers: { "Content-Type": "application/json" },
        },
      );
    } catch (error) {
      console.error("Error creating booking:", error);
      return new Response(
        JSON.stringify({ error: "Failed to create booking" }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        },
      );
    }
  }

  return new Response(JSON.stringify({ error: "Method not allowed" }), {
    status: 405,
    headers: { "Content-Type": "application/json" },
  });
}
