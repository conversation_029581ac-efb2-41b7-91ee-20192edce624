import { AppProvider as ShopifyAppProvider } from "@shopify/shopify-app-react-router/react";
import { A<PERSON><PERSON><PERSON><PERSON>, Page, Card, TextField, Button } from "@shopify/polaris";
import { useState } from "react";
import type { ActionFunctionArgs, LoaderFunctionArgs } from "react-router";
import { Form, useActionData, useLoaderData } from "react-router";

import { login } from "../../shopify.server";
import { loginErrorMessage } from "./error.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const errors = loginErrorMessage(await login(request));

  return { errors };
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const errors = loginErrorMessage(await login(request));

  return {
    errors,
  };
};

export default function Auth() {
  const loaderData = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const [shop, setShop] = useState("");
  const { errors } = actionData || loaderData;

  return (
    <ShopifyAppProvider embedded={false}>
      <AppProvider i18n={{}}>
        <Page title="Log in">
          <Form method="post">
            <Card>
              <TextField
                name="shop"
                label="Shop domain"
                helpText="example.myshopify.com"
                value={shop}
                onChange={(value) => setShop(value)}
                autoComplete="on"
                error={errors.shop}
              />
              <div style={{ marginTop: "1rem" }}>
                <Button submit>Log in</Button>
              </div>
            </Card>
          </Form>
        </Page>
      </AppProvider>
    </ShopifyAppProvider>
  );
}
