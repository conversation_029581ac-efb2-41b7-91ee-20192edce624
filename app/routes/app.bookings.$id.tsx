import type { ActionFunctionArgs, LoaderFunctionArgs } from "react-router";
import { use<PERSON><PERSON><PERSON><PERSON><PERSON>, useF<PERSON>cher, useNavigate } from "react-router";
import { useState, useEffect } from "react";
import { authenticate } from "../shopify.server";
import db from "../db.server";
import {
  Page,
  Card,
  Text,
  Checkbox,
  Select,
  TextField,
  Button,
  BlockStack,
  ButtonGroup,
  DataTable,
  Banner,
} from "@shopify/polaris";

// Types
interface BookingConfiguration {
  id: string;
  shop: string;
  productId: string;
  productTitle: string | null;
  isActive: boolean;
  slotType: "HOURLY" | "DAILY";
  slotDuration: number;
  advanceBookingDays: number;
  maxSlotsPerBooking: number;
  allowDiscontinuous: boolean;
  startTime: string | null;
  endTime: string | null;
  workingDays: string;
  basePriceOverride: number | null;
  pricePerSlot: number | null;
  enableDynamicPricing: boolean;
  bulkDiscountEnabled: boolean;
  peakPricingEnabled: boolean;
  bulkDiscountTiers: string;
  peakPricingRules: string;
  seasonalPricingRules: string;
  createdAt: Date;
  updatedAt: Date;
}

interface Product {
  id: string;
  title: string;
  handle: string;
  status: string;
  featuredImage?: {
    url: string;
    altText?: string;
  };
}

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const { admin, session } = await authenticate.admin(request);
  const configId = params.id;

  if (!configId) {
    throw new Response("Configuration ID is required", { status: 400 });
  }

  // Fetch the booking configuration
  const bookingConfiguration = await db.bookingConfiguration.findFirst({
    where: {
      id: configId,
      shop: session.shop,
    },
  });

  if (!bookingConfiguration) {
    throw new Response("Booking configuration not found", { status: 404 });
  }

  // Fetch the product details from Shopify
  const productResponse = await admin.graphql(
    `#graphql
      query getProduct($id: ID!) {
        product(id: $id) {
          id
          title
          handle
          status
          featuredImage {
            url
            altText
          }
        }
      }`,
    {
      variables: {
        id: bookingConfiguration.productId,
      },
    },
  );

  const productData = await productResponse.json();
  const product = productData.data?.product;

  return {
    bookingConfiguration,
    product,
    shop: session.shop,
  };
};

export const action = async ({ request, params }: ActionFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const configId = params.id;
  const formData = await request.formData();
  const action = formData.get("action");

  if (!configId) {
    return new Response(
      JSON.stringify({
        success: false,
        message: "Configuration ID is required",
      }),
      { status: 400, headers: { "Content-Type": "application/json" } },
    );
  }

  if (action === "generateSlots") {
    // Generate preview slots based on current configuration
    const config = await db.bookingConfiguration.findFirst({
      where: { id: configId, shop: session.shop },
    });

    if (!config) {
      return new Response(
        JSON.stringify({ success: false, message: "Configuration not found" }),
        { status: 404, headers: { "Content-Type": "application/json" } },
      );
    }

    // Generate slots for the next 7 days as preview
    const slots = [];
    const today = new Date();
    const workingDays = config.workingDays.split(",").map((d) => parseInt(d));

    for (let i = 0; i < 7; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      const dayOfWeek = date.getDay() === 0 ? 7 : date.getDay(); // Convert Sunday from 0 to 7

      if (workingDays.includes(dayOfWeek)) {
        if (
          config.slotType === "HOURLY" &&
          config.startTime &&
          config.endTime
        ) {
          const [startHour, startMin] = config.startTime
            .split(":")
            .map((n) => parseInt(n));
          const [endHour, endMin] = config.endTime
            .split(":")
            .map((n) => parseInt(n));

          const startDateTime = new Date(date);
          startDateTime.setHours(startHour, startMin, 0, 0);

          const endDateTime = new Date(date);
          endDateTime.setHours(endHour, endMin, 0, 0);

          let currentTime = new Date(startDateTime);
          while (currentTime < endDateTime) {
            const slotEnd = new Date(currentTime);
            slotEnd.setMinutes(currentTime.getMinutes() + config.slotDuration);

            if (slotEnd <= endDateTime) {
              slots.push({
                startDateTime: new Date(currentTime),
                endDateTime: new Date(slotEnd),
                date: date.toDateString(),
                timeRange: `${currentTime.toLocaleTimeString("en-US", { hour: "2-digit", minute: "2-digit" })} - ${slotEnd.toLocaleTimeString("en-US", { hour: "2-digit", minute: "2-digit" })}`,
              });
            }

            currentTime.setMinutes(
              currentTime.getMinutes() + config.slotDuration,
            );
          }
        } else if (config.slotType === "DAILY") {
          slots.push({
            startDateTime: new Date(date),
            endDateTime: new Date(date.getTime() + 24 * 60 * 60 * 1000),
            date: date.toDateString(),
            timeRange: "Full Day",
          });
        }
      }
    }

    return { success: true, slots: slots.slice(0, 20) }; // Limit to 20 slots for preview
  }

  if (action === "update") {
    const slotType = formData.get("slotType") as string;
    const slotDuration = parseInt(formData.get("slotDuration") as string);
    const advanceBookingDays = parseInt(
      formData.get("advanceBookingDays") as string,
    );
    const maxSlotsPerBooking = parseInt(
      formData.get("maxSlotsPerBooking") as string,
    );
    const allowDiscontinuous = formData.get("allowDiscontinuous") === "on";
    const startTime = formData.get("startTime") as string;
    const endTime = formData.get("endTime") as string;
    const workingDays = formData.get("workingDays") as string;
    const basePriceOverride = formData.get("basePriceOverride")
      ? parseFloat(formData.get("basePriceOverride") as string)
      : null;
    const pricePerSlot = formData.get("pricePerSlot")
      ? parseFloat(formData.get("pricePerSlot") as string)
      : null;
    const isActive = formData.get("isActive") === "on";

    // Dynamic pricing fields
    const enableDynamicPricing = formData.get("enableDynamicPricing") === "on";
    const bulkDiscountEnabled = formData.get("bulkDiscountEnabled") === "on";
    const peakPricingEnabled = formData.get("peakPricingEnabled") === "on";
    const bulkDiscountTiers =
      (formData.get("bulkDiscountTiers") as string) || "[]";
    const peakPricingRules =
      (formData.get("peakPricingRules") as string) || "[]";
    const seasonalPricingRules =
      (formData.get("seasonalPricingRules") as string) || "[]";

    try {
      await db.bookingConfiguration.update({
        where: {
          id: configId,
          shop: session.shop,
        },
        data: {
          slotType: slotType as "HOURLY" | "DAILY",
          slotDuration,
          advanceBookingDays,
          maxSlotsPerBooking,
          allowDiscontinuous,
          startTime: startTime || null,
          endTime: endTime || null,
          workingDays,
          basePriceOverride,
          pricePerSlot,
          isActive,
          enableDynamicPricing,
          bulkDiscountEnabled,
          peakPricingEnabled,
          bulkDiscountTiers,
          peakPricingRules,
          seasonalPricingRules,
        },
      });

      return {
        success: true,
        message: "Booking configuration updated successfully",
      };
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          message: "Failed to update booking configuration",
        }),
        { status: 400, headers: { "Content-Type": "application/json" } },
      );
    }
  }

  return new Response(
    JSON.stringify({ success: false, message: "Invalid action" }),
    { status: 400, headers: { "Content-Type": "application/json" } },
  );
};

export default function BookingConfigurationEdit() {
  const { bookingConfiguration, product } = useLoaderData<typeof loader>();
  const fetcher = useFetcher<typeof action>();
  const slotsFetcher = useFetcher<typeof action>();
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    slotType: bookingConfiguration.slotType,
    slotDuration: bookingConfiguration.slotDuration,
    advanceBookingDays: bookingConfiguration.advanceBookingDays,
    maxSlotsPerBooking: bookingConfiguration.maxSlotsPerBooking,
    allowDiscontinuous: bookingConfiguration.allowDiscontinuous,
    startTime: bookingConfiguration.startTime || "",
    endTime: bookingConfiguration.endTime || "",
    workingDays: bookingConfiguration.workingDays,
    basePriceOverride: bookingConfiguration.basePriceOverride || "",
    pricePerSlot: bookingConfiguration.pricePerSlot || "",
    isActive: bookingConfiguration.isActive,
    enableDynamicPricing: bookingConfiguration.enableDynamicPricing || false,
    bulkDiscountEnabled: bookingConfiguration.bulkDiscountEnabled || false,
    peakPricingEnabled: bookingConfiguration.peakPricingEnabled || false,
    bulkDiscountTiers: bookingConfiguration.bulkDiscountTiers || "[]",
    peakPricingRules: bookingConfiguration.peakPricingRules || "[]",
    seasonalPricingRules: bookingConfiguration.seasonalPricingRules || "[]",
  });
  const [showSlotPreview, setShowSlotPreview] = useState(false);

  const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    const form = new FormData(event.currentTarget);
    form.append("action", "update");
    fetcher.submit(form, { method: "post" });
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleGenerateSlots = () => {
    const form = new FormData();
    form.append("action", "generateSlots");
    slotsFetcher.submit(form, { method: "post" });
    setShowSlotPreview(true);
  };

  useEffect(() => {
    if (fetcher.data?.success) {
      // Show success message and optionally redirect
      setTimeout(() => {
        navigate("/app/bookings");
      }, 1000);
    }
  }, [fetcher.data, navigate]);

  return (
    <Page
      title={`Configure Booking: ${product?.title || "Product"}`}
      backAction={{
        content: "Booking Management",
        onAction: () => navigate("/app/bookings"),
      }}
      primaryAction={{
        content: "Save Configuration",
        onAction: () => {
          const form = document.querySelector(
            "form[data-save-bar]",
          ) as HTMLFormElement;
          if (form) {
            form.requestSubmit();
          }
        },
        loading: fetcher.state === "submitting",
      }}
      secondaryActions={[
        {
          content: "Cancel",
          onAction: () => navigate("/app/bookings"),
        },
      ]}
    >
      <form onSubmit={handleSubmit} data-save-bar>
        <Card>
          <Text variant="headingMd" as="h2">
            Product Information
          </Text>
          <div style={{ marginTop: "1rem" }}>
            <BlockStack gap="200">
              <Text variant="headingMd" as="h3">
                {product?.title}
              </Text>
              <Text as="p" tone="subdued">
                Product ID: {product?.id}
              </Text>
              <Text as="p" tone="subdued">
                Status: {product?.status}
              </Text>
            </BlockStack>
          </div>
        </Card>

        <Card>
          <Text variant="headingMd" as="h2">
            Booking Configuration
          </Text>
          <div style={{ marginTop: "1rem" }}>
            <BlockStack gap="400">
              <Checkbox
                label="Enable Booking"
                name="isActive"
                checked={formData.isActive}
                onChange={(checked) => handleInputChange("isActive", checked)}
              />

              <Select
                label="Slot Type"
                name="slotType"
                options={[
                  { label: "Hourly Slots", value: "HOURLY" },
                  { label: "Daily Slots", value: "DAILY" },
                ]}
                value={formData.slotType}
                onChange={(value) => handleInputChange("slotType", value)}
                requiredIndicator
              />

              <TextField
                label="Slot Duration (minutes)"
                name="slotDuration"
                value={formData.slotDuration.toString()}
                onChange={(value) =>
                  handleInputChange("slotDuration", parseInt(value) || 60)
                }
                type="number"
                min={1}
                helpText={
                  formData.slotType === "DAILY"
                    ? "For daily slots, use 1440 (24 hours)"
                    : "Duration of each booking slot in minutes"
                }
                requiredIndicator
                autoComplete="off"
              />

              <TextField
                label="Advance Booking Days"
                name="advanceBookingDays"
                value={formData.advanceBookingDays.toString()}
                onChange={(value) =>
                  handleInputChange("advanceBookingDays", parseInt(value) || 30)
                }
                type="number"
                min={1}
                helpText="How many days in advance customers can book"
                requiredIndicator
                autoComplete="off"
              />

              <TextField
                label="Max Slots Per Booking"
                name="maxSlotsPerBooking"
                value={formData.maxSlotsPerBooking.toString()}
                onChange={(value) =>
                  handleInputChange("maxSlotsPerBooking", parseInt(value) || 1)
                }
                type="number"
                min={1}
                helpText="Maximum number of consecutive slots per booking"
                requiredIndicator
                autoComplete="off"
              />

              <Checkbox
                label="Allow Discontinuous Booking"
                name="allowDiscontinuous"
                checked={formData.allowDiscontinuous}
                onChange={(checked) =>
                  handleInputChange("allowDiscontinuous", checked)
                }
                helpText="Allow customers to book non-consecutive time slots"
              />
            </BlockStack>
          </div>
        </Card>

        {formData.slotType === "HOURLY" && (
          <Card>
            <Text variant="headingMd" as="h2">
              Time Configuration
            </Text>
            <div style={{ marginTop: "1rem" }}>
              <BlockStack gap="400">
                <TextField
                  label="Start Time (HH:MM)"
                  name="startTime"
                  value={formData.startTime}
                  onChange={(value) => handleInputChange("startTime", value)}
                  placeholder="09:00"
                  helpText="Daily start time for bookings"
                  autoComplete="off"
                />

                <TextField
                  label="End Time (HH:MM)"
                  name="endTime"
                  value={formData.endTime}
                  onChange={(value) => handleInputChange("endTime", value)}
                  placeholder="17:00"
                  helpText="Daily end time for bookings"
                  autoComplete="off"
                />

                <TextField
                  label="Working Days"
                  name="workingDays"
                  value={formData.workingDays}
                  onChange={(value) => handleInputChange("workingDays", value)}
                  placeholder="1,2,3,4,5"
                  helpText="Comma-separated day numbers (1=Monday, 7=Sunday)"
                  autoComplete="off"
                />
              </BlockStack>
            </div>
          </Card>
        )}

        <Card>
          <Text variant="headingMd" as="h2">
            Pricing Configuration
          </Text>
          <div style={{ marginTop: "1rem" }}>
            <BlockStack gap="400">
              <TextField
                label="Base Price Override"
                name="basePriceOverride"
                value={formData.basePriceOverride.toString()}
                onChange={(value) =>
                  handleInputChange(
                    "basePriceOverride",
                    value ? parseFloat(value) : "",
                  )
                }
                type="number"
                step={0.01}
                min={0}
                helpText="Override the product's base price for bookings (leave empty to use product price)"
                autoComplete="off"
              />

              <TextField
                label="Price Per Slot"
                name="pricePerSlot"
                value={formData.pricePerSlot.toString()}
                onChange={(value) =>
                  handleInputChange(
                    "pricePerSlot",
                    value ? parseFloat(value) : "",
                  )
                }
                type="number"
                step={0.01}
                min={0}
                helpText="Additional price charged per booking slot"
                autoComplete="off"
              />
            </BlockStack>
          </div>
        </Card>

        <Card>
          <Text variant="headingMd" as="h2">
            Dynamic Pricing
          </Text>
          <div style={{ marginTop: "1rem" }}>
            <BlockStack gap="400">
              <Checkbox
                label="Enable Dynamic Pricing"
                name="enableDynamicPricing"
                checked={formData.enableDynamicPricing}
                onChange={(value) =>
                  handleInputChange("enableDynamicPricing", value)
                }
                helpText="Enable advanced pricing based on booking range, peak times, and seasonal rates"
              />

              {formData.enableDynamicPricing && (
                <BlockStack gap="400">
                  <Checkbox
                    label="Enable Bulk Discounts"
                    name="bulkDiscountEnabled"
                    checked={formData.bulkDiscountEnabled}
                    onChange={(value) =>
                      handleInputChange("bulkDiscountEnabled", value)
                    }
                    helpText="Offer discounts for booking multiple slots"
                  />

                  {formData.bulkDiscountEnabled && (
                    <TextField
                      label="Bulk Discount Tiers (JSON)"
                      name="bulkDiscountTiers"
                      value={formData.bulkDiscountTiers}
                      onChange={(value) =>
                        handleInputChange("bulkDiscountTiers", value)
                      }
                      multiline={3}
                      helpText='Example: [{"minSlots": 3, "discountPercent": 10}, {"minSlots": 5, "discountPercent": 20}]'
                      autoComplete="off"
                    />
                  )}

                  <Checkbox
                    label="Enable Peak Pricing"
                    name="peakPricingEnabled"
                    checked={formData.peakPricingEnabled}
                    onChange={(value) =>
                      handleInputChange("peakPricingEnabled", value)
                    }
                    helpText="Apply higher rates during peak times or days"
                  />

                  {formData.peakPricingEnabled && (
                    <TextField
                      label="Peak Pricing Rules (JSON)"
                      name="peakPricingRules"
                      value={formData.peakPricingRules}
                      onChange={(value) =>
                        handleInputChange("peakPricingRules", value)
                      }
                      multiline={3}
                      helpText='Example: [{"type": "weekday", "days": [6,7], "multiplier": 1.5}]'
                      autoComplete="off"
                    />
                  )}

                  <TextField
                    label="Seasonal Pricing Rules (JSON)"
                    name="seasonalPricingRules"
                    value={formData.seasonalPricingRules}
                    onChange={(value) =>
                      handleInputChange("seasonalPricingRules", value)
                    }
                    multiline={3}
                    helpText='Example: [{"startDate": "2024-12-20", "endDate": "2024-12-31", "multiplier": 2.0, "name": "Holiday Season"}]'
                    autoComplete="off"
                  />
                </BlockStack>
              )}
            </BlockStack>
          </div>
        </Card>

        <Card>
          <Text variant="headingMd" as="h2">
            Slot Preview
          </Text>
          <div style={{ marginTop: "1rem" }}>
            <BlockStack gap="400">
              <Text as="p" tone="subdued">
                Preview how booking slots will be generated based on your
                current configuration.
              </Text>

              <Button
                onClick={handleGenerateSlots}
                loading={slotsFetcher.state === "submitting"}
              >
                Generate Slot Preview
              </Button>

              {showSlotPreview && slotsFetcher.data?.success && (
                <BlockStack gap="400">
                  <Text variant="headingMd" as="h3">
                    Next 7 Days Preview
                  </Text>
                  {slotsFetcher.data.slots &&
                  slotsFetcher.data.slots.length > 0 ? (
                    <DataTable
                      columnContentTypes={["text", "text", "text"]}
                      headings={["Date", "Time", "Duration"]}
                      rows={slotsFetcher.data.slots.map((slot: any) => [
                        slot.date,
                        slot.timeRange,
                        `${formData.slotDuration} min`,
                      ])}
                    />
                  ) : (
                    <Text as="p" tone="subdued">
                      No slots generated. Check your time configuration and
                      working days.
                    </Text>
                  )}
                </BlockStack>
              )}

              {showSlotPreview &&
                slotsFetcher.data &&
                !slotsFetcher.data.success && (
                  <Banner tone="critical">
                    <Text as="p">
                      Failed to generate slot preview. Please check your
                      configuration.
                    </Text>
                  </Banner>
                )}
            </BlockStack>
          </div>
        </Card>
      </form>
    </Page>
  );
}
