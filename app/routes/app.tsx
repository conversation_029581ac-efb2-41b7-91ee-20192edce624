import type { HeadersFunction, LoaderFunctionArgs } from "react-router";
import {
  Outlet,
  useLoaderData,
  useRouteError,
  useLocation,
} from "react-router";
import { boundary } from "@shopify/shopify-app-react-router/server";
import { AppProvider as ShopifyAppProvider } from "@shopify/shopify-app-react-router/react";
import { AppProvider, Navigation, Frame } from "@shopify/polaris";

import { authenticate } from "../shopify.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  await authenticate.admin(request);

  // eslint-disable-next-line no-undef
  return { apiKey: process.env.SHOPIFY_API_KEY || "" };
};

export default function App() {
  const { apiKey } = useLoaderData<typeof loader>();
  const location = useLocation();

  return (
    <ShopifyAppProvider embedded apiKey={apiKey}>
      <AppProvider
        i18n={{
          Polaris: {
            Common: {
              checkbox: "Checkbox",
              undo: "Undo",
              cancel: "Cancel",
              clear: "Clear",
              close: "Close",
              submit: "Submit",
              more: "More",
            },
          },
        }}
        features={{ newDesignLanguage: true }}
      >
        <Frame
          navigation={
            <Navigation location={location.pathname}>
              <Navigation.Section
                items={[
                  {
                    url: "/app",
                    label: "Home",
                    selected: location.pathname === "/app",
                  },
                  {
                    url: "/app/bookings",
                    label: "Booking Management",
                    selected: location.pathname.startsWith("/app/bookings"),
                  },
                  {
                    url: "/app/additional",
                    label: "Additional page",
                    selected: location.pathname === "/app/additional",
                  },
                ]}
              />
            </Navigation>
          }
        >
          <Outlet />
        </Frame>
      </AppProvider>
    </ShopifyAppProvider>
  );
}

// Shopify needs React Router to catch some thrown responses, so that their headers are included in the response.
export function ErrorBoundary() {
  const error = useRouteError();

  // Log error for debugging
  console.error("App route error:", error);

  return boundary.error(error);
}

export const headers: HeadersFunction = (headersArgs) => {
  return boundary.headers(headersArgs);
};
