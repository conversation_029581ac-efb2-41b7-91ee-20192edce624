import type { ActionFunctionArgs, LoaderFunctionArgs } from "react-router";
import { useLoader<PERSON><PERSON>, useFetcher, useSubmit } from "react-router";
import { useEffect, useState } from "react";
import { authenticate } from "../shopify.server";
import db from "../db.server";
import {
  Page,
  Card,
  Button,
  Banner,
  Text,
  Modal,
  BlockStack,
  Select,
  TextField,
  Checkbox,
  ButtonGroup,
  DataTable,
  Badge,
  EmptyState,
} from "@shopify/polaris";

// Types
interface Product {
  node: {
    id: string;
    title: string;
  };
}

interface BookingConfiguration {
  id: string;
  productId: string;
  productTitle: string | null;
  slotType: "HOURLY" | "DAILY";
  slotDuration: number;
  isActive: boolean;
}

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { admin, session } = await authenticate.admin(request);

  // Fetch products from Shopify
  const productsResponse = await admin.graphql(
    `#graphql
      query getProducts($first: Int!) {
        products(first: $first) {
          edges {
            node {
              id
              title
              handle
              status
              featuredImage {
                url
                altText
              }
            }
          }
        }
      }`,
    {
      variables: {
        first: 50,
      },
    },
  );

  const productsData = await productsResponse.json();
  const products = productsData.data?.products?.edges || [];

  // Fetch existing booking configurations
  const bookingConfigurations = await db.bookingConfiguration.findMany({
    where: {
      shop: session.shop,
    },
    orderBy: {
      createdAt: "desc",
    },
  });

  return {
    products,
    bookingConfigurations,
    shop: session.shop,
  };
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const formData = await request.formData();
  const action = formData.get("action");

  if (action === "create") {
    const productId = formData.get("productId") as string;
    const productTitle = formData.get("productTitle") as string;
    const slotType = formData.get("slotType") as string;
    const slotDuration = parseInt(formData.get("slotDuration") as string);
    const advanceBookingDays = parseInt(
      formData.get("advanceBookingDays") as string,
    );
    const maxSlotsPerBooking = parseInt(
      formData.get("maxSlotsPerBooking") as string,
    );
    const allowDiscontinuous = formData.get("allowDiscontinuous") === "true";
    const startTime = formData.get("startTime") as string;
    const endTime = formData.get("endTime") as string;
    const workingDays = formData.get("workingDays") as string;
    const basePriceOverride = formData.get("basePriceOverride")
      ? parseFloat(formData.get("basePriceOverride") as string)
      : null;
    const pricePerSlot = formData.get("pricePerSlot")
      ? parseFloat(formData.get("pricePerSlot") as string)
      : null;

    try {
      await db.bookingConfiguration.create({
        data: {
          shop: session.shop,
          productId,
          productTitle,
          slotType: slotType as "HOURLY" | "DAILY",
          slotDuration,
          advanceBookingDays,
          maxSlotsPerBooking,
          allowDiscontinuous,
          startTime: startTime || null,
          endTime: endTime || null,
          workingDays,
          basePriceOverride,
          pricePerSlot,
        },
      });

      return {
        success: true,
        message: "Booking configuration created successfully",
      };
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          message: "Failed to create booking configuration",
        }),
        { status: 400, headers: { "Content-Type": "application/json" } },
      );
    }
  }

  if (action === "delete") {
    const configId = formData.get("configId") as string;

    try {
      await db.bookingConfiguration.delete({
        where: {
          id: configId,
          shop: session.shop,
        },
      });

      return {
        success: true,
        message: "Booking configuration deleted successfully",
      };
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          message: "Failed to delete booking configuration",
        }),
        { status: 400, headers: { "Content-Type": "application/json" } },
      );
    }
  }

  if (action === "toggle") {
    const configId = formData.get("configId") as string;
    const isActive = formData.get("isActive") === "true";

    try {
      await db.bookingConfiguration.update({
        where: {
          id: configId,
          shop: session.shop,
        },
        data: {
          isActive: !isActive,
        },
      });

      return {
        success: true,
        message: "Booking configuration updated successfully",
      };
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          message: "Failed to update booking configuration",
        }),
        { status: 400, headers: { "Content-Type": "application/json" } },
      );
    }
  }

  return new Response(
    JSON.stringify({ success: false, message: "Invalid action" }),
    { status: 400, headers: { "Content-Type": "application/json" } },
  );
};

export default function BookingManagement() {
  const { products, bookingConfigurations } = useLoaderData<typeof loader>();
  const fetcher = useFetcher<typeof action>();
  const submit = useSubmit();
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState("");

  // Filter products that don't have booking configurations
  const availableProducts = products.filter(
    (product: Product) =>
      !bookingConfigurations.some(
        (config: BookingConfiguration) => config.productId === product.node.id,
      ),
  );

  const handleCreateConfig = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    const formData = new FormData(event.currentTarget);
    formData.append("action", "create");
    submit(formData, { method: "post" });
    setShowCreateForm(false);
    setSelectedProduct("");
  };

  const handleToggleActive = (configId: string, isActive: boolean) => {
    const formData = new FormData();
    formData.append("action", "toggle");
    formData.append("configId", configId);
    formData.append("isActive", isActive.toString());
    submit(formData, { method: "post" });
  };

  const handleDelete = (configId: string) => {
    if (
      confirm("Are you sure you want to delete this booking configuration?")
    ) {
      const formData = new FormData();
      formData.append("action", "delete");
      formData.append("configId", configId);
      submit(formData, { method: "post" });
    }
  };

  useEffect(() => {
    if (fetcher.data?.success) {
      // Show success message or handle success
    }
  }, [fetcher.data]);

  return (
    <Page
      title="Booking Management"
      primaryAction={{
        content: "Create Booking Configuration",
        onAction: () => setShowCreateForm(true),
        disabled: availableProducts.length === 0,
      }}
    >
      {availableProducts.length === 0 && (
        <Banner tone="info">
          <Text as="p">
            All products already have booking configurations. Create more
            products or remove existing configurations to add new ones.
          </Text>
        </Banner>
      )}

      {showCreateForm && (
        <Modal
          open={showCreateForm}
          onClose={() => setShowCreateForm(false)}
          title="Create Booking Configuration"
          primaryAction={{
            content: "Create Configuration",
            onAction: () => {
              const form = document.querySelector(
                "form[data-create-config]",
              ) as HTMLFormElement;
              if (form) {
                form.requestSubmit();
              }
            },
          }}
          secondaryActions={[
            {
              content: "Cancel",
              onAction: () => setShowCreateForm(false),
            },
          ]}
        >
          <Modal.Section>
            <form onSubmit={handleCreateConfig} data-create-config>
              <BlockStack gap="400">
                <Select
                  label="Select Product"
                  options={[
                    { label: "Choose a product...", value: "" },
                    ...availableProducts.map((product: Product) => ({
                      label: product.node.title,
                      value: product.node.id,
                    })),
                  ]}
                  value={selectedProduct}
                  onChange={(value) => setSelectedProduct(value)}
                  name="productId"
                  requiredIndicator
                />

                {selectedProduct && (
                  <input
                    type="hidden"
                    name="productTitle"
                    value={
                      availableProducts.find(
                        (p: Product) => p.node.id === selectedProduct,
                      )?.node.title || ""
                    }
                  />
                )}

                <Select
                  label="Slot Type"
                  options={[
                    { label: "Hourly", value: "HOURLY" },
                    { label: "Daily", value: "DAILY" },
                  ]}
                  value="HOURLY"
                  name="slotType"
                  requiredIndicator
                />

                <TextField
                  label="Slot Duration (minutes)"
                  name="slotDuration"
                  value="60"
                  type="number"
                  min={1}
                  requiredIndicator
                  autoComplete="off"
                />

                <TextField
                  label="Advance Booking Days"
                  name="advanceBookingDays"
                  value="30"
                  type="number"
                  min={1}
                  requiredIndicator
                  autoComplete="off"
                />

                <TextField
                  label="Max Slots Per Booking"
                  name="maxSlotsPerBooking"
                  value="1"
                  type="number"
                  min={1}
                  requiredIndicator
                  autoComplete="off"
                />

                <Checkbox
                  label="Allow Discontinuous Booking"
                  name="allowDiscontinuous"
                />

                <TextField
                  label="Start Time (HH:MM)"
                  name="startTime"
                  placeholder="09:00"
                  autoComplete="off"
                />

                <TextField
                  label="End Time (HH:MM)"
                  name="endTime"
                  placeholder="17:00"
                  autoComplete="off"
                />

                <TextField
                  label="Working Days (1=Mon, 7=Sun)"
                  name="workingDays"
                  value="1,2,3,4,5"
                  placeholder="1,2,3,4,5"
                  autoComplete="off"
                />

                <TextField
                  label="Base Price Override"
                  name="basePriceOverride"
                  type="number"
                  step={0.01}
                  min={0}
                  autoComplete="off"
                />

                <TextField
                  label="Price Per Slot"
                  name="pricePerSlot"
                  type="number"
                  step={0.01}
                  min={0}
                  autoComplete="off"
                />
              </BlockStack>
            </form>
          </Modal.Section>
        </Modal>
      )}

      <Card>
        <Text variant="headingMd" as="h2">
          Existing Booking Configurations
        </Text>
        {bookingConfigurations.length === 0 ? (
          <div style={{ marginTop: "1rem" }}>
            <EmptyState
              heading="No booking configurations yet"
              action={{
                content: "Create your first configuration",
                onAction: () => setShowCreateForm(true),
                disabled: availableProducts.length === 0,
              }}
              image="https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png"
            >
              <Text as="p">
                Create booking configurations to enable appointment scheduling
                for your products.
              </Text>
            </EmptyState>
          </div>
        ) : (
          <div style={{ marginTop: "1rem" }}>
            <DataTable
              columnContentTypes={["text", "text", "text", "text", "text"]}
              headings={["Product", "Type", "Duration", "Status", "Actions"]}
              rows={bookingConfigurations.map(
                (config: BookingConfiguration) => [
                  config.productTitle,
                  config.slotType,
                  `${config.slotDuration} min`,
                  <Badge
                    key={`status-${config.id}`}
                    tone={config.isActive ? "success" : "critical"}
                  >
                    {config.isActive ? "Active" : "Inactive"}
                  </Badge>,
                  <ButtonGroup key={`actions-${config.id}`}>
                    <Button
                      onClick={() =>
                        (window.location.href = `/app/bookings/${config.id}`)
                      }
                    >
                      Edit
                    </Button>
                    <Button
                      onClick={() =>
                        handleToggleActive(config.id, config.isActive)
                      }
                    >
                      {config.isActive ? "Deactivate" : "Activate"}
                    </Button>
                    <Button
                      tone="critical"
                      onClick={() => handleDelete(config.id)}
                    >
                      Delete
                    </Button>
                  </ButtonGroup>,
                ],
              )}
            />
          </div>
        )}
      </Card>
    </Page>
  );
}
