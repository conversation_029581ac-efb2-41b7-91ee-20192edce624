import type { LoaderFunctionArgs } from "react-router";
import { authenticate } from "../shopify.server";
import prisma from "../db.server";
import { BookingPricingService } from "../services/pricing.server";

export async function loader({ request }: LoaderFunctionArgs) {
  const { session } = await authenticate.public.appProxy(request);

  if (!session) {
    return new Response(JSON.stringify({ error: "Unauthorized" }), {
      status: 401,
      headers: { "Content-Type": "application/json" },
    });
  }

  const url = new URL(request.url);
  const productId = url.searchParams.get("productId");
  const slotsParam = url.searchParams.get("slots");
  const basePrice = parseFloat(url.searchParams.get("basePrice") || "0");

  if (!productId || !slotsParam) {
    return new Response(
      JSON.stringify({ error: "Product ID and slots are required" }),
      {
        status: 400,
        headers: { "Content-Type": "application/json" },
      },
    );
  }

  try {
    // Parse slots from JSON
    const slots = JSON.parse(slotsParam);

    // Get booking configuration for the product
    const bookingConfig = await prisma.bookingConfiguration.findFirst({
      where: {
        productId: productId,
        isActive: true,
      },
    });

    if (!bookingConfig) {
      return new Response(
        JSON.stringify({
          error: "No booking configuration found for this product",
        }),
        {
          status: 404,
          headers: { "Content-Type": "application/json" },
        },
      );
    }

    // Calculate pricing using the pricing service
    const pricingResult = BookingPricingService.calculateBookingPrice(
      bookingConfig,
      slots,
      basePrice,
    );

    return new Response(
      JSON.stringify({
        pricing: pricingResult,
        config: {
          slotType: bookingConfig.slotType,
          slotDuration: bookingConfig.slotDuration,
          enableDynamicPricing: bookingConfig.enableDynamicPricing,
        },
      }),
      {
        headers: { "Content-Type": "application/json" },
      },
    );
  } catch (error) {
    console.error("Error calculating booking pricing:", error);
    return new Response(
      JSON.stringify({ error: "Failed to calculate pricing" }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" },
      },
    );
  }
}

export async function action({ request }: LoaderFunctionArgs) {
  const { session } = await authenticate.public.appProxy(request);

  if (!session) {
    return new Response(JSON.stringify({ error: "Unauthorized" }), {
      status: 401,
      headers: { "Content-Type": "application/json" },
    });
  }

  if (request.method === "POST") {
    try {
      const body = await request.json();
      const { productId, slots, basePrice = 0 } = body;

      if (!productId || !slots) {
        return new Response(
          JSON.stringify({ error: "Product ID and slots are required" }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" },
          },
        );
      }

      // Get booking configuration
      const bookingConfig = await prisma.bookingConfiguration.findFirst({
        where: {
          productId: productId,
          isActive: true,
        },
      });

      if (!bookingConfig) {
        return new Response(
          JSON.stringify({ error: "No booking configuration found" }),
          {
            status: 404,
            headers: { "Content-Type": "application/json" },
          },
        );
      }

      // Calculate pricing
      const pricingResult = BookingPricingService.calculateBookingPrice(
        bookingConfig,
        slots,
        basePrice,
      );

      return new Response(
        JSON.stringify({
          success: true,
          pricing: pricingResult,
        }),
        {
          headers: { "Content-Type": "application/json" },
        },
      );
    } catch (error) {
      console.error("Error calculating booking pricing:", error);
      return new Response(
        JSON.stringify({ error: "Failed to calculate pricing" }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        },
      );
    }
  }

  return new Response(JSON.stringify({ error: "Method not allowed" }), {
    status: 405,
    headers: { "Content-Type": "application/json" },
  });
}
