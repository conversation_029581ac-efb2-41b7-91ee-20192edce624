import { Page, Card, Text, Link, List, Layout } from "@shopify/polaris";

export default function AdditionalPage() {
  return (
    <Page title="Additional page">
      <Layout>
        <Layout.Section>
          <Card>
            <Text variant="headingMd" as="h2">
              Multiple pages
            </Text>
            <div style={{ marginTop: "1rem" }}>
              <Text as="p">
                The app template comes with an additional page which
                demonstrates how to create multiple pages within app navigation
                using{" "}
                <Link
                  url="https://shopify.dev/docs/apps/tools/app-bridge"
                  external
                >
                  App Bridge
                </Link>
                .
              </Text>
            </div>
            <div style={{ marginTop: "1rem" }}>
              <Text as="p">
                To create your own page and have it show up in the app
                navigation, add a page inside <code>app/routes</code>, and a
                link to it in the <code>&lt;ui-nav-menu&gt;</code> component
                found in <code>app/routes/app.jsx</code>.
              </Text>
            </div>
          </Card>
        </Layout.Section>
        <Layout.Section variant="oneThird">
          <Card>
            <Text variant="headingMd" as="h2">
              Resources
            </Text>
            <div style={{ marginTop: "1rem" }}>
              <List>
                <List.Item>
                  <Link
                    url="https://shopify.dev/docs/apps/design-guidelines/navigation#app-nav"
                    external
                  >
                    App nav best practices
                  </Link>
                </List.Item>
              </List>
            </div>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
