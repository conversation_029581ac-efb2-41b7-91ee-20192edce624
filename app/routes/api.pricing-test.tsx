import type { LoaderFunctionArgs } from "react-router";
import { BookingPricingService } from "../services/pricing.server";

export async function loader({ request }: LoaderFunctionArgs) {
  // Test the pricing service with mock data
  const mockConfig = {
    id: "test-config",
    basePriceOverride: 50,
    pricePerSlot: 10,
    enableDynamicPricing: true,
    bulkDiscountEnabled: true,
    peakPricingEnabled: true,
    bulkDiscountTiers: JSON.stringify([
      { minSlots: 3, discountPercent: 10, name: "3+ Slots Discount" },
      { minSlots: 5, discountPercent: 20, name: "5+ Slots Discount" },
    ]),
    peakPricingRules: JSON.stringify([
      {
        type: "weekday",
        days: [6, 7],
        multiplier: 1.5,
        name: "Weekend Premium",
      },
      {
        type: "time",
        startTime: "18:00",
        endTime: "22:00",
        multiplier: 1.2,
        name: "Evening Premium",
      },
    ]),
    seasonalPricingRules: JSON.stringify([
      {
        startDate: "2024-12-20",
        endDate: "2024-12-31",
        multiplier: 2.0,
        name: "Holiday Season",
      },
    ]),
    slotType: "HOURLY" as const,
    slotDuration: 60,
  };

  const testCases = [
    {
      name: "Single slot - weekday morning",
      slots: [{ date: "2024-10-01", time: "10:00", duration: 60 }], // Tuesday
      basePrice: 100,
    },
    {
      name: "3 slots - bulk discount",
      slots: [
        { date: "2024-10-01", time: "10:00", duration: 60 },
        { date: "2024-10-01", time: "11:00", duration: 60 },
        { date: "2024-10-01", time: "12:00", duration: 60 },
      ],
      basePrice: 100,
    },
    {
      name: "Weekend slot - peak pricing",
      slots: [{ date: "2024-10-05", time: "10:00", duration: 60 }], // Saturday
      basePrice: 100,
    },
    {
      name: "Evening slot - peak pricing",
      slots: [{ date: "2024-10-01", time: "19:00", duration: 60 }], // Tuesday evening
      basePrice: 100,
    },
    {
      name: "5 slots - maximum bulk discount",
      slots: [
        { date: "2024-10-01", time: "10:00", duration: 60 },
        { date: "2024-10-01", time: "11:00", duration: 60 },
        { date: "2024-10-01", time: "12:00", duration: 60 },
        { date: "2024-10-01", time: "13:00", duration: 60 },
        { date: "2024-10-01", time: "14:00", duration: 60 },
      ],
      basePrice: 100,
    },
  ];

  const results = testCases.map((testCase) => {
    const pricing = BookingPricingService.calculateBookingPrice(
      mockConfig,
      testCase.slots,
      testCase.basePrice,
    );

    return {
      testCase: testCase.name,
      slots: testCase.slots.length,
      originalPrice: pricing.breakdown.originalPrice,
      finalPrice: pricing.totalPrice,
      savings: pricing.breakdown.savings,
      adjustments: pricing.adjustments.map((adj) => ({
        type: adj.type,
        name: adj.name,
        amount: adj.amount,
      })),
    };
  });

  return new Response(
    JSON.stringify({
      message: "Pricing Service Test Results",
      config: {
        basePriceOverride: mockConfig.basePriceOverride,
        pricePerSlot: mockConfig.pricePerSlot,
        enableDynamicPricing: mockConfig.enableDynamicPricing,
        bulkDiscountEnabled: mockConfig.bulkDiscountEnabled,
        peakPricingEnabled: mockConfig.peakPricingEnabled,
      },
      results,
    }),
    {
      headers: { "Content-Type": "application/json" },
    },
  );
}
