import { useEffect } from "react";
import type {
  ActionFunctionArgs,
  HeadersFunction,
  LoaderFunctionArgs,
} from "react-router";
import { useFetcher } from "react-router";
import { useAppBridge } from "@shopify/app-bridge-react";
import { authenticate } from "../shopify.server";
import { boundary } from "@shopify/shopify-app-react-router/server";
import {
  Page,
  Card,
  Text,
  Button,
  Link,
  Layout,
  BlockStack,
  InlineStack,
  Box,
  List,
} from "@shopify/polaris";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  await authenticate.admin(request);

  return null;
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const { admin } = await authenticate.admin(request);
  const color = ["Red", "Orange", "Yellow", "Green"][
    Math.floor(Math.random() * 4)
  ];
  const response = await admin.graphql(
    `#graphql
      mutation populateProduct($product: ProductCreateInput!) {
        productCreate(product: $product) {
          product {
            id
            title
            handle
            status
            variants(first: 10) {
              edges {
                node {
                  id
                  price
                  barcode
                  createdAt
                }
              }
            }
          }
        }
      }`,
    {
      variables: {
        product: {
          title: `${color} Snowboard`,
        },
      },
    },
  );
  const responseJson = await response.json();

  const product = responseJson.data!.productCreate!.product!;
  const variantId = product.variants.edges[0]!.node!.id!;

  const variantResponse = await admin.graphql(
    `#graphql
    mutation shopifyReactRouterTemplateUpdateVariant($productId: ID!, $variants: [ProductVariantsBulkInput!]!) {
      productVariantsBulkUpdate(productId: $productId, variants: $variants) {
        productVariants {
          id
          price
          barcode
          createdAt
        }
      }
    }`,
    {
      variables: {
        productId: product.id,
        variants: [{ id: variantId, price: "100.00" }],
      },
    },
  );

  const variantResponseJson = await variantResponse.json();

  return {
    product: responseJson!.data!.productCreate!.product,
    variant:
      variantResponseJson!.data!.productVariantsBulkUpdate!.productVariants,
  };
};

export default function Index() {
  const fetcher = useFetcher<typeof action>();

  const shopify = useAppBridge();
  const isLoading =
    ["loading", "submitting"].includes(fetcher.state) &&
    fetcher.formMethod === "POST";
  const productId = fetcher.data?.product?.id.replace(
    "gid://shopify/Product/",
    "",
  );

  useEffect(() => {
    if (productId) {
      shopify.toast.show("Product created");
    }
  }, [productId, shopify]);
  const generateProduct = () => fetcher.submit({}, { method: "POST" });

  return (
    <Page
      title="React Router app template"
      primaryAction={{
        content: "Generate a product",
        onAction: generateProduct,
        loading: isLoading,
      }}
    >
      <Layout>
        <Layout.Section>
          <Card>
            <Text variant="headingMd" as="h2">
              Congrats on creating a new Shopify app 🎉
            </Text>
            <div style={{ marginTop: "1rem" }}>
              <Text as="p">
                This embedded app template uses{" "}
                <Link
                  url="https://shopify.dev/docs/apps/tools/app-bridge"
                  external
                >
                  App Bridge
                </Link>{" "}
                interface examples like an{" "}
                <Link url="/app/additional">
                  additional page in the app nav
                </Link>
                , as well as an{" "}
                <Link url="https://shopify.dev/docs/api/admin-graphql" external>
                  Admin GraphQL
                </Link>{" "}
                mutation demo, to provide a starting point for app development.
              </Text>
            </div>
          </Card>
        </Layout.Section>
        <Layout.Section>
          <Card>
            <Text variant="headingMd" as="h2">
              Get started with products
            </Text>
            <div style={{ marginTop: "1rem" }}>
              <Text as="p">
                Generate a product with GraphQL and get the JSON output for that
                product. Learn more about the{" "}
                <Link
                  url="https://shopify.dev/docs/api/admin-graphql/latest/mutations/productCreate"
                  external
                >
                  productCreate
                </Link>{" "}
                mutation in our API references.
              </Text>
            </div>
            <div style={{ marginTop: "1rem" }}>
              <InlineStack gap="400">
                <Button onClick={generateProduct} loading={isLoading}>
                  Generate a product
                </Button>
                {fetcher.data?.product && (
                  <Button
                    url={`shopify:admin/products/${productId}`}
                    external
                    variant="plain"
                  >
                    View product
                  </Button>
                )}
              </InlineStack>
            </div>
            {fetcher.data?.product && (
              <div style={{ marginTop: "1.5rem" }}>
                <Text variant="headingMd" as="h3">
                  productCreate mutation
                </Text>
                <div style={{ marginTop: "1rem" }}>
                  <BlockStack gap="400">
                    <Box
                      padding="400"
                      borderWidth="025"
                      borderRadius="200"
                      background="bg-surface-secondary"
                    >
                      <pre style={{ margin: 0 }}>
                        <code>
                          {JSON.stringify(fetcher.data.product, null, 2)}
                        </code>
                      </pre>
                    </Box>

                    <Text variant="headingMd" as="h4">
                      productVariantsBulkUpdate mutation
                    </Text>
                    <Box
                      padding="400"
                      borderWidth="025"
                      borderRadius="200"
                      background="bg-surface-secondary"
                    >
                      <pre style={{ margin: 0 }}>
                        <code>
                          {JSON.stringify(fetcher.data.variant, null, 2)}
                        </code>
                      </pre>
                    </Box>
                  </BlockStack>
                </div>
              </div>
            )}
          </Card>
        </Layout.Section>

        <Layout.Section variant="oneThird">
          <Card>
            <Text variant="headingMd" as="h2">
              App template specs
            </Text>
            <div style={{ marginTop: "1rem" }}>
              <BlockStack gap="200">
                <Text as="p">
                  <Text as="span" fontWeight="semibold">
                    Framework:{" "}
                  </Text>
                  <Link url="https://reactrouter.com/" external>
                    React Router
                  </Link>
                </Text>
                <Text as="p">
                  <Text as="span" fontWeight="semibold">
                    Interface:{" "}
                  </Text>
                  <Link
                    url="https://shopify.dev/docs/api/app-home/using-polaris-components"
                    external
                  >
                    Polaris React components
                  </Link>
                </Text>
                <Text as="p">
                  <Text as="span" fontWeight="semibold">
                    API:{" "}
                  </Text>
                  <Link
                    url="https://shopify.dev/docs/api/admin-graphql"
                    external
                  >
                    GraphQL
                  </Link>
                </Text>
                <Text as="p">
                  <Text as="span" fontWeight="semibold">
                    Database:{" "}
                  </Text>
                  <Link url="https://www.prisma.io/" external>
                    Prisma
                  </Link>
                </Text>
              </BlockStack>
            </div>
          </Card>
        </Layout.Section>

        <Layout.Section variant="oneThird">
          <Card>
            <Text variant="headingMd" as="h2">
              Next steps
            </Text>
            <div style={{ marginTop: "1rem" }}>
              <List>
                <List.Item>
                  Build an{" "}
                  <Link
                    url="https://shopify.dev/docs/apps/getting-started/build-app-example"
                    external
                  >
                    example app
                  </Link>
                </List.Item>
                <List.Item>
                  Explore Shopify&apos;s API with{" "}
                  <Link
                    url="https://shopify.dev/docs/apps/tools/graphiql-admin-api"
                    external
                  >
                    GraphiQL
                  </Link>
                </List.Item>
              </List>
            </div>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
}

export const headers: HeadersFunction = (headersArgs) => {
  return boundary.headers(headersArgs);
};
