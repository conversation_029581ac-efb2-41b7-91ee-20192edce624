-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_BookingConfiguration" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "shop" TEXT NOT NULL,
    "productId" TEXT NOT NULL,
    "productTitle" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "slotType" TEXT NOT NULL DEFAULT 'HOURLY',
    "slotDuration" INTEGER NOT NULL DEFAULT 60,
    "advanceBookingDays" INTEGER NOT NULL DEFAULT 30,
    "maxSlotsPerBooking" INTEGER NOT NULL DEFAULT 1,
    "allowDiscontinuous" BOOLEAN NOT NULL DEFAULT false,
    "startTime" TEXT,
    "endTime" TEXT,
    "workingDays" TEXT NOT NULL DEFAULT '1,2,3,4,5',
    "basePriceOverride" REAL,
    "pricePerSlot" REAL,
    "enableDynamicPricing" BOOLEAN NOT NULL DEFAULT false,
    "bulkDiscountEnabled" BOOLEAN NOT NULL DEFAULT false,
    "peakPricingEnabled" BOOLEAN NOT NULL DEFAULT false,
    "bulkDiscountTiers" TEXT DEFAULT '[]',
    "peakPricingRules" TEXT DEFAULT '[]',
    "seasonalPricingRules" TEXT DEFAULT '[]',
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);
INSERT INTO "new_BookingConfiguration" ("advanceBookingDays", "allowDiscontinuous", "basePriceOverride", "createdAt", "endTime", "id", "isActive", "maxSlotsPerBooking", "pricePerSlot", "productId", "productTitle", "shop", "slotDuration", "slotType", "startTime", "updatedAt", "workingDays") SELECT "advanceBookingDays", "allowDiscontinuous", "basePriceOverride", "createdAt", "endTime", "id", "isActive", "maxSlotsPerBooking", "pricePerSlot", "productId", "productTitle", "shop", "slotDuration", "slotType", "startTime", "updatedAt", "workingDays" FROM "BookingConfiguration";
DROP TABLE "BookingConfiguration";
ALTER TABLE "new_BookingConfiguration" RENAME TO "BookingConfiguration";
CREATE INDEX "BookingConfiguration_shop_isActive_idx" ON "BookingConfiguration"("shop", "isActive");
CREATE UNIQUE INDEX "BookingConfiguration_shop_productId_key" ON "BookingConfiguration"("shop", "productId");
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;
