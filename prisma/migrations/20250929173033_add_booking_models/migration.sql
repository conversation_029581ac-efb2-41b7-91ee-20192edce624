-- CreateTable
CREATE TABLE "BookingConfiguration" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "shop" TEXT NOT NULL,
    "productId" TEXT NOT NULL,
    "productTitle" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "slotType" TEXT NOT NULL DEFAULT 'HOURLY',
    "slotDuration" INTEGER NOT NULL DEFAULT 60,
    "advanceBookingDays" INTEGER NOT NULL DEFAULT 30,
    "maxSlotsPerBooking" INTEGER NOT NULL DEFAULT 1,
    "allowDiscontinuous" BOOLEAN NOT NULL DEFAULT false,
    "startTime" TEXT,
    "endTime" TEXT,
    "workingDays" TEXT NOT NULL DEFAULT '1,2,3,4,5',
    "basePriceOverride" REAL,
    "pricePerSlot" REAL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);

-- CreateTable
CREATE TABLE "BookingSlot" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "configurationId" TEXT NOT NULL,
    "startDateTime" DATETIME NOT NULL,
    "endDateTime" DATETIME NOT NULL,
    "isAvailable" BOOLEAN NOT NULL DEFAULT true,
    "maxCapacity" INTEGER NOT NULL DEFAULT 1,
    "currentBookings" INTEGER NOT NULL DEFAULT 0,
    "priceOverride" REAL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "BookingSlot_configurationId_fkey" FOREIGN KEY ("configurationId") REFERENCES "BookingConfiguration" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "Booking" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "configurationId" TEXT NOT NULL,
    "customerId" TEXT,
    "customerEmail" TEXT NOT NULL,
    "customerName" TEXT NOT NULL,
    "customerPhone" TEXT,
    "orderId" TEXT,
    "lineItemId" TEXT,
    "totalPrice" REAL NOT NULL,
    "currency" TEXT NOT NULL DEFAULT 'USD',
    "status" TEXT NOT NULL DEFAULT 'PENDING',
    "notes" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "Booking_configurationId_fkey" FOREIGN KEY ("configurationId") REFERENCES "BookingConfiguration" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "BookingSlotItem" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "bookingId" TEXT NOT NULL,
    "slotId" TEXT NOT NULL,
    "price" REAL NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'PENDING',
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "BookingSlotItem_bookingId_fkey" FOREIGN KEY ("bookingId") REFERENCES "Booking" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "BookingSlotItem_slotId_fkey" FOREIGN KEY ("slotId") REFERENCES "BookingSlot" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateIndex
CREATE INDEX "BookingConfiguration_shop_isActive_idx" ON "BookingConfiguration"("shop", "isActive");

-- CreateIndex
CREATE UNIQUE INDEX "BookingConfiguration_shop_productId_key" ON "BookingConfiguration"("shop", "productId");

-- CreateIndex
CREATE INDEX "BookingSlot_configurationId_startDateTime_isAvailable_idx" ON "BookingSlot"("configurationId", "startDateTime", "isAvailable");

-- CreateIndex
CREATE UNIQUE INDEX "BookingSlot_configurationId_startDateTime_key" ON "BookingSlot"("configurationId", "startDateTime");

-- CreateIndex
CREATE INDEX "Booking_configurationId_status_idx" ON "Booking"("configurationId", "status");

-- CreateIndex
CREATE INDEX "Booking_customerId_idx" ON "Booking"("customerId");

-- CreateIndex
CREATE INDEX "Booking_orderId_idx" ON "Booking"("orderId");

-- CreateIndex
CREATE INDEX "BookingSlotItem_slotId_status_idx" ON "BookingSlotItem"("slotId", "status");

-- CreateIndex
CREATE UNIQUE INDEX "BookingSlotItem_bookingId_slotId_key" ON "BookingSlotItem"("bookingId", "slotId");
