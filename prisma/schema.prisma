// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

// Note that some adapters may set a maximum length for the String type by default, please ensure your strings are long
// enough when changing adapters.
// See https://www.prisma.io/docs/orm/reference/prisma-schema-reference#string for more information
datasource db {
  provider = "sqlite"
  url      = "file:dev.sqlite"
}

model Session {
  id            String    @id
  shop          String
  state         String
  isOnline      <PERSON>an   @default(false)
  scope         String?
  expires       DateTime?
  accessToken   String
  userId        BigInt?
  firstName     String?
  lastName      String?
  email         String?
  accountOwner  Boolean   @default(false)
  locale        String?
  collaborator  <PERSON><PERSON><PERSON>?  @default(false)
  emailVerified <PERSON>olean?  @default(false)
}

// Booking Configuration for Products
model BookingConfiguration {
  id           String  @id @default(cuid())
  shop         String // Shopify shop domain
  productId    String // Shopify Product ID (gid://shopify/Product/...)
  productTitle String? // Cache product title for easier querying
  isActive     Boolean @default(true)

  // Booking Type Configuration
  slotType     SlotType @default(HOURLY) // HOURLY or DAILY
  slotDuration Int      @default(60) // Duration in minutes (for hourly) or 1440 for daily

  // Availability Configuration
  advanceBookingDays Int     @default(30) // How many days in advance can be booked
  maxSlotsPerBooking Int     @default(1) // Maximum consecutive slots per booking
  allowDiscontinuous Boolean @default(false) // Allow non-consecutive slot booking

  // Time Configuration (for hourly slots)
  startTime   String? // "09:00" format
  endTime     String? // "17:00" format
  workingDays String  @default("1,2,3,4,5") // Comma-separated day numbers (1=Monday, 7=Sunday)

  // Pricing Configuration
  basePriceOverride Float? // Override product price for bookings
  pricePerSlot      Float? // Additional price per slot

  // Dynamic Pricing Configuration
  enableDynamicPricing Boolean @default(false) // Enable dynamic pricing based on booking range
  bulkDiscountEnabled  Boolean @default(false) // Enable bulk discounts for multiple slots
  peakPricingEnabled   Boolean @default(false) // Enable peak pricing for specific dates/times

  // Bulk Discount Tiers (JSON format)
  // Example: [{"minSlots": 3, "discountPercent": 10}, {"minSlots": 5, "discountPercent": 20}]
  bulkDiscountTiers String? @default("[]")

  // Peak Pricing Rules (JSON format)
  // Example: [{"type": "weekday", "days": [6,7], "multiplier": 1.5}, {"type": "time", "startTime": "18:00", "endTime": "22:00", "multiplier": 1.2}]
  peakPricingRules String? @default("[]")

  // Seasonal Pricing (JSON format)
  // Example: [{"startDate": "2024-12-20", "endDate": "2024-12-31", "multiplier": 2.0, "name": "Holiday Season"}]
  seasonalPricingRules String? @default("[]")

  // Metadata
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  bookingSlots BookingSlot[]
  bookings     Booking[]

  @@unique([shop, productId])
  @@index([shop, isActive])
}

// Individual time slots that can be booked
model BookingSlot {
  id              String @id @default(cuid())
  configurationId String

  // Slot Details
  startDateTime   DateTime
  endDateTime     DateTime
  isAvailable     Boolean  @default(true)
  maxCapacity     Int      @default(1)
  currentBookings Int      @default(0)

  // Pricing
  priceOverride Float? // Override price for this specific slot

  // Metadata
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  configuration    BookingConfiguration @relation(fields: [configurationId], references: [id], onDelete: Cascade)
  bookingSlotItems BookingSlotItem[]

  @@unique([configurationId, startDateTime])
  @@index([configurationId, startDateTime, isAvailable])
}

// Customer bookings
model Booking {
  id              String @id @default(cuid())
  configurationId String

  // Customer Information
  customerId    String? // Shopify Customer ID
  customerEmail String
  customerName  String
  customerPhone String?

  // Order Information
  orderId    String? // Shopify Order ID
  lineItemId String? // Shopify Line Item ID

  // Booking Details
  totalPrice Float
  currency   String        @default("USD")
  status     BookingStatus @default(PENDING)

  // Special Requirements
  notes String?

  // Metadata
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  configuration    BookingConfiguration @relation(fields: [configurationId], references: [id])
  bookingSlotItems BookingSlotItem[]

  @@index([configurationId, status])
  @@index([customerId])
  @@index([orderId])
}

// Junction table for booking slots and bookings (many-to-many)
model BookingSlotItem {
  id        String @id @default(cuid())
  bookingId String
  slotId    String

  // Item Details
  price  Float // Price paid for this slot
  status BookingStatus @default(PENDING)

  // Metadata
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  booking Booking     @relation(fields: [bookingId], references: [id], onDelete: Cascade)
  slot    BookingSlot @relation(fields: [slotId], references: [id], onDelete: Cascade)

  @@unique([bookingId, slotId])
  @@index([slotId, status])
}

// Enums
enum SlotType {
  HOURLY
  DAILY
}

enum BookingStatus {
  PENDING
  CONFIRMED
  CANCELLED
  COMPLETED
  NO_SHOW
}
